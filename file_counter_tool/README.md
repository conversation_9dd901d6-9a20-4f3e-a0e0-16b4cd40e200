# Datasets Utility

A collection of tools for analyzing and processing research datasets.

## Tools Available

### File Counter Tool
Located in `file_counter_tool/`

A utility for analyzing WAV files in participant subdirectories and generating CSV reports with task counts.

**Quick Start:**
```bash
cd file_counter_tool
python3 wav_file_counter.py /path/to/your/data
```

**Features:**
- Analyzes WAV files in subdirectories starting with "sub-"
- Extracts participant IDs and task codes
- Generates CSV reports with task counts
- Supports predefined task types: TPL, PCT, GFTA, PLSS, CELFS, CELFT

See `file_counter_tool/README.md` for detailed documentation.

## Project Structure

```
datasets-utility/
├── README.md                    # This file
└── file_counter_tool/           # WAV file analysis tool
    ├── README.md               # Detailed documentation
    ├── wav_file_counter.py     # Main analysis script
    ├── test_wav_counter.py     # Test script with sample data
    ├── usage_example.py        # Quick usage reference
    └── requirements.txt        # Dependencies (Python stdlib only)
```

## Requirements

- Python 3.6 or higher
- No external dependencies (uses Python standard library only)

## Contributing

Feel free to add more dataset analysis tools to this repository by creating new subdirectories with their own documentation.
