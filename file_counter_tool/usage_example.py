#!/usr/bin/env python3
"""
Quick usage example for the WAV file counter script
"""

import os
import subprocess

def show_help():
    """Show basic usage information"""
    print("WAV File Counter - Quick Usage")
    print("=" * 40)
    print()
    print("BASIC USAGE:")
    print("  python3 wav_file_counter.py <root_directory>")
    print()
    print("EXAMPLE:")
    print("  python3 wav_file_counter.py /path/to/data")
    print()
    print("WITH CUSTOM OUTPUT:")
    print("  python3 wav_file_counter.py /path/to/data my_report.csv")
    print()
    print("GET HELP:")
    print("  python3 wav_file_counter.py --help")
    print()
    print("RUN TEST:")
    print("  python3 test_wav_counter.py")
    print()
    print("RUN FROM PARENT DIRECTORY:")
    print("  cd file_counter_tool && python3 wav_file_counter.py /path/to/data")
    print()

def show_expected_structure():
    """Show expected directory structure"""
    print("EXPECTED DIRECTORY STRUCTURE:")
    print("=" * 40)
    print("""
your_data/
├── sub-001/                    # Participant folder (starts with 'sub-')
│   ├── recording_task-TPL.wav  # WAV file with task code
│   ├── session_task-PCT.wav    # Another WAV file
│   └── data_task-GFTA.wav     # Pattern: *_task-<CODE>.wav
├── sub-002/
│   ├── audio_task-PLSS.wav
│   └── speech_task-CELFS.wav
├── other-folder/               # Will be ignored (doesn't start with 'sub-')
│   └── ignored.wav
└── sub-003/
    └── nested/                 # Nested directories are supported
        └── deep_task-CELFT.wav
    """)

def show_output_format():
    """Show expected CSV output format"""
    print("OUTPUT CSV FORMAT:")
    print("=" * 40)
    print("""
ParticipantID,TPL,PCT,GFTA,PLSS,CELFS,CELFT,Other
sub-001,2,1,1,0,0,0,1
sub-002,0,0,0,1,1,0,0
sub-003,0,0,0,0,0,1,0

Where:
- ParticipantID: Folder name that starts with 'sub-'
- TPL, PCT, etc.: Count of WAV files for each task type
- Other: Count of WAV files with unrecognized task codes
    """)

if __name__ == "__main__":
    show_help()
    show_expected_structure()
    show_output_format()
