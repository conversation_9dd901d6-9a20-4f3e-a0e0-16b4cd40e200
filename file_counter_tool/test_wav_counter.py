#!/usr/bin/env python3
"""
Test script to create sample data and test the analyze_audio_files.py script
"""

import os
import shutil
import subprocess
from pathlib import Path


def create_test_data():
    """Create test directory structure with sample WAV files"""
    test_dir = "test_data"
    
    # Remove existing test data if it exists
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
    
    # Create directory structure
    test_dirs = [
        "test_data/sub-001",
        "test_data/sub-002", 
        "test_data/sub-003/nested",
        "test_data/other-folder",  # This should be ignored
        "test_data/regular-folder"  # This should be ignored
    ]
    
    for dir_path in test_dirs:
        os.makedirs(dir_path, exist_ok=True)
    
    # Create sample WAV files with task codes
    test_files = [
        # Participant sub-001
        "test_data/sub-001/recording_task-TPL.wav",
        "test_data/sub-001/session_task-TPL.wav",  # Another TPL file
        "test_data/sub-001/data_task-PCT.wav",
        "test_data/sub-001/test_task-GFTA.wav",
        "test_data/sub-001/experiment_task-UNKNOWN.wav",  # Should go to Other
        
        # Participant sub-002
        "test_data/sub-002/audio_task-PLSS.wav",
        "test_data/sub-002/speech_task-CELFS.wav",
        "test_data/sub-002/language_task-CELFT.wav",
        
        # Participant sub-003 (nested directory)
        "test_data/sub-003/nested/deep_task-PCT.wav",
        "test_data/sub-003/nested/another_task-CELFS.wav",
        
        # Files in ignored directories
        "test_data/other-folder/ignored_task-TPL.wav",
        "test_data/regular-folder/also_ignored_task-PCT.wav",
        
        # File without task code (should be skipped with warning)
        "test_data/sub-001/no_task_code.wav"
    ]
    
    # Create empty files (simulating WAV files)
    for file_path in test_files:
        Path(file_path).touch()
    
    print(f"Created test data in '{test_dir}' directory:")
    print("Directory structure:")
    for root, dirs, files in os.walk(test_dir):
        level = root.replace(test_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")
    
    return test_dir


def run_analysis(test_dir):
    """Run the analysis script on test data"""
    print("\n" + "="*50)
    print("Running analysis script...")
    print("="*50)
    
    try:
        result = subprocess.run(
            ["python3", "wav_file_counter.py", test_dir, "test_output.csv"],
            capture_output=True,
            text=True,
            check=True
        )
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
            
    except subprocess.CalledProcessError as e:
        print(f"Error running script: {e}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False
    
    return True


def show_results():
    """Display the generated CSV results"""
    csv_file = "test_output.csv"
    if os.path.exists(csv_file):
        print("\n" + "="*50)
        print("Generated CSV content:")
        print("="*50)
        with open(csv_file, 'r') as f:
            print(f.read())
    else:
        print(f"CSV file '{csv_file}' not found")


def cleanup():
    """Clean up test files"""
    print("\n" + "="*50)
    print("Cleanup")
    print("="*50)
    
    # Ask user if they want to keep test files
    response = input("Do you want to keep the test files? (y/n): ").lower().strip()
    
    if response != 'y' and response != 'yes':
        if os.path.exists("test_data"):
            shutil.rmtree("test_data")
            print("Removed test_data directory")
        
        if os.path.exists("test_output.csv"):
            os.remove("test_output.csv")
            print("Removed test_output.csv")
    else:
        print("Test files kept:")
        print("  - test_data/ (directory with sample data)")
        print("  - test_output.csv (generated results)")


def main():
    """Main test function"""
    print("File Counter Tool - Test Script")
    print("="*50)
    
    # Create test data
    test_dir = create_test_data()
    
    # Run analysis
    if run_analysis(test_dir):
        # Show results
        show_results()
    
    # Cleanup
    cleanup()


if __name__ == "__main__":
    main()
