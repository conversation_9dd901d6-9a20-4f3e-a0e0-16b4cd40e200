#!/usr/bin/env python3
"""
WAV File Counter Script

This script analyzes WAV files in subdirectories that start with 'sub-' and generates
a CSV report with participant IDs and task counts.

Usage:
    python3 wav_file_counter.py <root_directory> [output_csv]

Arguments:
    root_directory: Path to the root directory containing sub- folders
    output_csv: Optional output CSV filename (default: wav_file_analysis.csv)
"""

import os
import sys
import csv
import re
from pathlib import Path
from collections import defaultdict
import argparse


def extract_participant_id(filepath):
    """
    Extract participant ID from filepath by finding folder names that start with 'sub-'
    
    Args:
        filepath (str): Full path to the file
        
    Returns:
        str: Participant ID (the sub- folder name) or None if not found
    """
    path_parts = Path(filepath).parts
    for part in path_parts:
        if part.startswith('sub-'):
            return part
    return None


def extract_task_code(filename):
    """
    Extract task code from filename using pattern _task-<taskcode>
    
    Args:
        filename (str): Name of the file
        
    Returns:
        str: Task code or None if not found
    """
    # Pattern to match _task-<taskcode>
    pattern = r'_task-([A-Za-z0-9]+)'
    match = re.search(pattern, filename)
    if match:
        return match.group(1).upper()  # Convert to uppercase for consistency
    return None


def find_wav_files(root_directory):
    """
    Find all WAV files in subdirectories that start with 'sub-'
    
    Args:
        root_directory (str): Root directory to search
        
    Returns:
        list: List of tuples (filepath, filename) for all WAV files found
    """
    wav_files = []
    root_path = Path(root_directory)
    
    if not root_path.exists():
        print(f"Error: Directory '{root_directory}' does not exist.")
        return wav_files
    
    # Iterate through immediate subdirectories
    for item in root_path.iterdir():
        if item.is_dir() and item.name.startswith('sub-'):
            print(f"Scanning directory: {item.name}")
            # Recursively find all .wav files in this subdirectory
            for wav_file in item.rglob('*.wav'):
                wav_files.append((str(wav_file), wav_file.name))
    
    return wav_files


def analyze_files(root_directory):
    """
    Analyze WAV files and count them by participant and task
    
    Args:
        root_directory (str): Root directory to analyze
        
    Returns:
        dict: Nested dictionary with participant IDs as keys and task counts as values
    """
    # Define the expected task codes
    expected_tasks = {'TPL', 'PCT', 'GFTA', 'PLSS', 'CELFS', 'CELFT'}
    
    # Initialize data structure
    participant_data = defaultdict(lambda: defaultdict(int))
    
    # Find all WAV files
    wav_files = find_wav_files(root_directory)
    
    if not wav_files:
        print("No WAV files found in subdirectories starting with 'sub-'")
        return participant_data
    
    print(f"Found {len(wav_files)} WAV files")
    
    # Process each WAV file
    for filepath, filename in wav_files:
        # Extract participant ID
        participant_id = extract_participant_id(filepath)
        if not participant_id:
            print(f"Warning: Could not extract participant ID from {filepath}")
            continue
        
        # Extract task code
        task_code = extract_task_code(filename)
        if not task_code:
            print(f"Warning: Could not extract task code from {filename}")
            continue
        
        # Categorize task code
        if task_code in expected_tasks:
            participant_data[participant_id][task_code] += 1
        else:
            participant_data[participant_id]['Other'] += 1
        
        print(f"  {participant_id}: {task_code} -> {filename}")
    
    return participant_data


def generate_csv(participant_data, output_file):
    """
    Generate CSV output with participant data
    
    Args:
        participant_data (dict): Data structure with participant and task counts
        output_file (str): Output CSV filename
    """
    # Define column headers
    task_columns = ['TPL', 'PCT', 'GFTA', 'PLSS', 'CELFS', 'CELFT', 'Other']
    headers = ['ParticipantID'] + task_columns
    
    # Sort participants for consistent output
    sorted_participants = sorted(participant_data.keys())
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header
            writer.writerow(headers)
            
            # Write data rows
            for participant_id in sorted_participants:
                row = [participant_id]
                for task in task_columns:
                    count = participant_data[participant_id].get(task, 0)
                    row.append(count)
                writer.writerow(row)
        
        print(f"\nCSV report generated: {output_file}")
        print(f"Total participants: {len(sorted_participants)}")
        
        # Print summary
        print("\nSummary:")
        for participant_id in sorted_participants:
            total_files = sum(participant_data[participant_id].values())
            print(f"  {participant_id}: {total_files} files")
            
    except Exception as e:
        print(f"Error writing CSV file: {e}")


def main():
    """Main function to run the analysis"""
    parser = argparse.ArgumentParser(
        description='Analyze WAV files in sub- directories and generate CSV report'
    )
    parser.add_argument(
        'root_directory',
        help='Root directory containing sub- folders'
    )
    parser.add_argument(
        'output_csv',
        nargs='?',
        default='wav_file_analysis.csv',
        help='Output CSV filename (default: wav_file_analysis.csv)'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )
    
    args = parser.parse_args()
    
    # Validate input directory
    if not os.path.exists(args.root_directory):
        print(f"Error: Directory '{args.root_directory}' does not exist.")
        sys.exit(1)
    
    if not os.path.isdir(args.root_directory):
        print(f"Error: '{args.root_directory}' is not a directory.")
        sys.exit(1)
    
    print(f"Analyzing WAV files in: {args.root_directory}")
    print(f"Output CSV: {args.output_csv}")
    print("-" * 50)
    
    # Analyze files
    participant_data = analyze_files(args.root_directory)
    
    if not participant_data:
        print("No data found. Please check that:")
        print("1. The root directory contains subdirectories starting with 'sub-'")
        print("2. Those subdirectories contain WAV files")
        print("3. The WAV files have task codes in format '_task-<taskcode>'")
        sys.exit(1)
    
    # Generate CSV
    generate_csv(participant_data, args.output_csv)


if __name__ == "__main__":
    main()
