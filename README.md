# Datasets Utility

A collection of tools for analyzing and processing research datasets.

## Tools Available

### File Counter Tool
Located in `file_counter_tool/`

A utility for analyzing WAV files in participant subdirectories on local filesystems and generating CSV reports with task counts.

**Quick Start:**
```bash
cd file_counter_tool
python3 wav_file_counter.py /path/to/your/data
```

### SharePoint File Counter Tool
Located in `sharepoint_file_counter_tool/`

A utility for counting WAV files directly from SharePoint Online without downloading them, generating the same CSV format as the local tool.

**Quick Start:**
```bash
cd sharepoint_file_counter_tool
pip install -r requirements.txt
python3 sharepoint_wav_counter.py
```

**Common Features:**
- Analyzes WAV files in subdirectories starting with "sub-"
- Extracts participant IDs and task codes
- Generates CSV reports with task counts
- Supports predefined task types: TPL, PCT, GFTA, PLSS, CELFS, CELFT

See individual tool README files for detailed documentation.

## Project Structure

```
datasets-utility/
├── README.md                          # This file
├── file_counter_tool/                 # Local WAV file analysis tool
│   ├── README.md                     # Local tool documentation
│   ├── wav_file_counter.py           # Main local analysis script
│   ├── test_wav_counter.py           # Test script with sample data
│   ├── usage_example.py              # Quick usage reference
│   └── requirements.txt              # No dependencies needed
└── sharepoint_file_counter_tool/     # SharePoint WAV file counter
    ├── README.md                     # SharePoint tool documentation
    ├── sharepoint_wav_counter.py     # Main SharePoint analysis script
    ├── test_sharepoint_counter.py    # Test script with mock data
    ├── usage_example.py              # Quick usage reference
    └── requirements.txt              # SharePoint dependencies
```

## Requirements

### Local File Counter Tool
- Python 3.6 or higher
- No external dependencies (uses Python standard library only)

### SharePoint File Counter Tool  
- Python 3.6 or higher
- Office365-REST-Python-Client
- pandas
- psutil

## Tool Comparison

| Feature | Local Tool | SharePoint Tool |
|---------|------------|-----------------|
| **Data Source** | Local filesystem | SharePoint Online |
| **Speed** | Very fast | Slower (network dependent) |
| **Setup** | No dependencies | Requires pip install |
| **Authentication** | Not required | SharePoint credentials |
| **Storage** | Requires local data | No local storage needed |
| **Resume capability** | No | Yes (checkpoints) |
| **Memory management** | Basic | Advanced |
| **Best for** | Local data analysis | Remote SharePoint datasets |

## Contributing

Feel free to add more dataset analysis tools to this repository by creating new subdirectories with their own documentation.
