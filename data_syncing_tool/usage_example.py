#!/usr/bin/env python3
"""
Usage example for the CSV comparison tool.

This script demonstrates how to use the csv_comparison.py tool with sample data.
"""

import os
import sys
from pathlib import Path

# Add the current directory to Python path to import the comparison module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from csv_comparison import compare_csv_files
import json


def create_sample_files():
    """Create sample CSV files for demonstration."""
    
    # Sample file 1
    sample1_content = """ParticipantID,TPL,PCT,GFTA,PLSS,CELFS,CELFT,Other
sub-ATL001,0,0,0,0,0,0,4
sub-ATL002,0,0,3,0,0,0,15
sub-ATL003,0,0,4,0,0,0,27
sub-BLT001,0,0,0,0,0,0,4
sub-BLT002,0,0,4,0,0,0,21
sub-CHI001,0,0,4,0,0,0,24
"""

    # Sample file 2 (with some differences)
    sample2_content = """ParticipantID,TPL,PCT,GFTA,PLSS,CELFS,CELFT,Other
sub-ATL001,0,0,0,0,0,0,4
sub-ATL002,0,0,3,0,0,0,18
sub-ATL003,0,0,5,0,0,0,27
sub-BLT002,0,0,4,0,0,0,21
sub-CHI001,0,0,4,0,0,0,24
sub-CHI002,0,0,0,0,0,0,18
"""
    
    # Write sample files
    with open('sample1.csv', 'w') as f:
        f.write(sample1_content)
    
    with open('sample2.csv', 'w') as f:
        f.write(sample2_content)
    
    print("Created sample files: sample1.csv and sample2.csv")


def run_example():
    """Run the comparison example."""
    print("=" * 60)
    print("CSV Comparison Tool - Usage Example")
    print("=" * 60)
    
    # Create sample files
    create_sample_files()
    
    print("\nComparing sample1.csv and sample2.csv...")
    print("\nSample1.csv contains:")
    print("- sub-ATL001, sub-ATL002, sub-ATL003, sub-BLT001, sub-BLT002, sub-CHI001")
    
    print("\nSample2.csv contains:")
    print("- sub-ATL001, sub-ATL002, sub-ATL003, sub-BLT002, sub-CHI001, sub-CHI002")
    
    print("\nExpected differences:")
    print("- Only in file1: sub-BLT001")
    print("- Only in file2: sub-CHI002")
    print("- Mismatched counts: sub-ATL002 (Other: 15 vs 18), sub-ATL003 (GFTA: 4 vs 5)")
    
    # Run comparison
    try:
        result = compare_csv_files('sample1.csv', 'sample2.csv')
        
        print("\n" + "=" * 60)
        print("COMPARISON RESULTS")
        print("=" * 60)
        
        print(f"\nSummary:")
        summary = result['comparison_summary']
        print(f"  Total participants in file1: {summary['total_participants_file1']}")
        print(f"  Total participants in file2: {summary['total_participants_file2']}")
        print(f"  Common participants: {summary['common_participants']}")
        print(f"  Only in file1: {summary['file1_only_count']}")
        print(f"  Only in file2: {summary['file2_only_count']}")
        print(f"  Mismatched counts: {summary['mismatched_count']}")
        
        print(f"\nParticipants only in file1: {result['file1_only']}")
        print(f"Participants only in file2: {result['file2_only']}")
        
        print(f"\nParticipants with mismatched counts:")
        for mismatch in result['mismatched_counts']:
            print(f"  {mismatch['participant_id']}:")
            print(f"    Differences in: {', '.join(mismatch['differences'])}")
            for diff_col in mismatch['differences']:
                file1_val = mismatch['file1_counts'][diff_col]
                file2_val = mismatch['file2_counts'][diff_col]
                print(f"    {diff_col}: {file1_val} (file1) vs {file2_val} (file2)")
        
        # Save full result as JSON
        with open('comparison_result.json', 'w') as f:
            json.dump(result, f, indent=2)
        print(f"\nFull results saved to: comparison_result.json")
        
        print("\n" + "=" * 60)
        print("USAGE EXAMPLES")
        print("=" * 60)
        print("\n1. Basic comparison (output to stdout):")
        print("   python csv_comparison.py file1.csv file2.csv")
        
        print("\n2. Save output to JSON file:")
        print("   python csv_comparison.py file1.csv file2.csv -o results.json")
        
        print("\n3. Pretty print JSON output:")
        print("   python csv_comparison.py file1.csv file2.csv --pretty")
        
        print("\n4. Combine options:")
        print("   python csv_comparison.py file1.csv file2.csv -o results.json --pretty")
        
    except Exception as e:
        print(f"Error during comparison: {e}")
    
    finally:
        # Clean up sample files
        for file in ['sample1.csv', 'sample2.csv']:
            if Path(file).exists():
                os.remove(file)
        print(f"\nCleaned up sample files.")


if __name__ == "__main__":
    run_example()
