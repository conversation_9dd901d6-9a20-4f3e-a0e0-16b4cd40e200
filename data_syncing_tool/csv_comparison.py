#!/usr/bin/env python3
"""
CSV Comparison Tool

This script compares two CSV files with participant data and generates a JSON report
showing differences in participant IDs and file counts.

Expected CSV format:
ParticipantID,TPL,PCT,GFTA,PLSS,CELFS,CELFT,Other
sub-XXX001,0,0,4,0,0,0,24
...

Output JSON format:
{
    "file1_only": ["sub-XXX001", ...],
    "file2_only": ["sub-YYY001", ...],
    "mismatched_counts": [
        {
            "participant_id": "sub-ZZZ001",
            "file1_counts": {"TPL": 0, "PCT": 0, ...},
            "file2_counts": {"TPL": 1, "PCT": 0, ...},
            "differences": ["TPL"]
        }
    ]
}
"""

import csv
import json
import argparse
import sys
from pathlib import Path
from typing import Dict, List, Set, Any


def read_csv_data(file_path: str) -> Dict[str, Dict[str, int]]:
    """
    Read CSV file and return a dictionary mapping ParticipantID to file counts.
    
    Args:
        file_path: Path to the CSV file
        
    Returns:
        Dictionary with ParticipantID as key and count data as value
    """
    data = {}
    count_columns = ['TPL', 'PCT', 'GFTA', 'PLSS', 'CELFS', 'CELFT', 'Other']
    
    try:
        with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            # Verify required columns exist
            if 'ParticipantID' not in reader.fieldnames:
                raise ValueError(f"ParticipantID column not found in {file_path}")
            
            missing_columns = [col for col in count_columns if col not in reader.fieldnames]
            if missing_columns:
                raise ValueError(f"Missing columns in {file_path}: {missing_columns}")
            
            for row in reader:
                participant_id = row['ParticipantID'].strip()
                if not participant_id:
                    continue
                    
                # Convert count values to integers
                counts = {}
                for col in count_columns:
                    try:
                        counts[col] = int(row[col])
                    except ValueError:
                        print(f"Warning: Invalid value '{row[col]}' for {col} in participant {participant_id}, using 0")
                        counts[col] = 0
                
                data[participant_id] = counts
                
    except FileNotFoundError:
        raise FileNotFoundError(f"File not found: {file_path}")
    except Exception as e:
        raise Exception(f"Error reading {file_path}: {str(e)}")
    
    return data


def compare_csv_files(file1_path: str, file2_path: str) -> Dict[str, Any]:
    """
    Compare two CSV files and return differences.
    
    Args:
        file1_path: Path to first CSV file
        file2_path: Path to second CSV file
        
    Returns:
        Dictionary containing comparison results
    """
    # Read both files
    data1 = read_csv_data(file1_path)
    data2 = read_csv_data(file2_path)
    
    # Get participant ID sets
    participants1 = set(data1.keys())
    participants2 = set(data2.keys())
    
    # Find participants only in file1 or file2
    file1_only = sorted(list(participants1 - participants2))
    file2_only = sorted(list(participants2 - participants1))
    
    # Find participants with mismatched counts
    common_participants = participants1 & participants2
    mismatched_counts = []
    
    count_columns = ['TPL', 'PCT', 'GFTA', 'PLSS', 'CELFS', 'CELFT', 'Other']
    
    for participant_id in sorted(common_participants):
        counts1 = data1[participant_id]
        counts2 = data2[participant_id]
        
        differences = []
        for col in count_columns:
            if counts1[col] != counts2[col]:
                differences.append(col)
        
        if differences:
            mismatched_counts.append({
                "participant_id": participant_id,
                "file1_counts": counts1,
                "file2_counts": counts2,
                "differences": differences
            })
    
    return {
        "comparison_summary": {
            "file1_path": file1_path,
            "file2_path": file2_path,
            "total_participants_file1": len(participants1),
            "total_participants_file2": len(participants2),
            "common_participants": len(common_participants),
            "file1_only_count": len(file1_only),
            "file2_only_count": len(file2_only),
            "mismatched_count": len(mismatched_counts)
        },
        "file1_only": file1_only,
        "file2_only": file2_only,
        "mismatched_counts": mismatched_counts
    }


def main():
    """Main function to handle command line arguments and execute comparison."""
    parser = argparse.ArgumentParser(
        description='Compare two CSV files with participant data and generate JSON difference report',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python csv_comparison.py file1.csv file2.csv
  python csv_comparison.py file1.csv file2.csv -o differences.json
  python csv_comparison.py file1.csv file2.csv --pretty
        """
    )
    
    parser.add_argument('file1', help='First CSV file path')
    parser.add_argument('file2', help='Second CSV file path')
    parser.add_argument('-o', '--output', help='Output JSON file path (default: print to stdout)')
    parser.add_argument('--pretty', action='store_true', help='Pretty print JSON output')
    
    args = parser.parse_args()
    
    # Validate input files exist
    if not Path(args.file1).exists():
        print(f"Error: File '{args.file1}' does not exist", file=sys.stderr)
        sys.exit(1)
    
    if not Path(args.file2).exists():
        print(f"Error: File '{args.file2}' does not exist", file=sys.stderr)
        sys.exit(1)
    
    try:
        # Perform comparison
        result = compare_csv_files(args.file1, args.file2)
        
        # Generate JSON output
        if args.pretty:
            json_output = json.dumps(result, indent=2, ensure_ascii=False)
        else:
            json_output = json.dumps(result, ensure_ascii=False)
        
        # Output results
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(json_output)
            print(f"Comparison results written to: {args.output}")
        else:
            print(json_output)
            
        # Print summary to stderr for convenience
        summary = result['comparison_summary']
        print(f"\nSummary:", file=sys.stderr)
        print(f"  Participants only in file1: {summary['file1_only_count']}", file=sys.stderr)
        print(f"  Participants only in file2: {summary['file2_only_count']}", file=sys.stderr)
        print(f"  Participants with mismatched counts: {summary['mismatched_count']}", file=sys.stderr)
        
    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
