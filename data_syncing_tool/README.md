# CSV Comparison Tool

This tool compares two CSV files containing participant data and generates a JSON report highlighting differences in participant IDs and file counts.

## Features

- **Participant ID Comparison**: Identifies participants present in one file but not the other
- **Count Mismatch Detection**: Finds participants with different values in TPL, PCT, GFTA, PLSS, CELFS, CELFT, or Other columns
- **JSON Output**: Generates structured JSON reports for easy processing
- **Detailed Summary**: Provides summary statistics and detailed difference information

## Requirements

- Python 3.6 or higher
- No external dependencies (uses only standard library)

## CSV File Format

The tool expects CSV files with the following format:

```csv
ParticipantID,TPL,PCT,GFTA,PLSS,CELFS,CELFT,Other
sub-ATL001,0,0,0,0,0,0,4
sub-ATL002,0,0,3,0,0,0,15
sub-ATL003,0,0,4,0,0,0,27
...
```

### Required Columns

- `ParticipantID`: Unique identifier for each participant
- `TPL`: Count value (integer)
- `PCT`: Count value (integer)
- `GFTA`: Count value (integer)
- `PLSS`: Count value (integer)
- `CELFS`: Count value (integer)
- `CELFT`: Count value (integer)
- `Other`: Count value (integer)

## Usage

### Basic Usage

```bash
python csv_comparison.py file1.csv file2.csv
```

### Save Output to File

```bash
python csv_comparison.py file1.csv file2.csv -o results.json
```

### Pretty Print JSON

```bash
python csv_comparison.py file1.csv file2.csv --pretty
```

### Combined Options

```bash
python csv_comparison.py file1.csv file2.csv -o results.json --pretty
```

## Output Format

The tool generates a JSON report with the following structure:

```json
{
  "comparison_summary": {
    "file1_path": "file1.csv",
    "file2_path": "file2.csv",
    "total_participants_file1": 150,
    "total_participants_file2": 148,
    "common_participants": 145,
    "file1_only_count": 5,
    "file2_only_count": 3,
    "mismatched_count": 7
  },
  "file1_only": [
    "sub-ATL001",
    "sub-BLT005"
  ],
  "file2_only": [
    "sub-CHI099",
    "sub-DLS100"
  ],
  "mismatched_counts": [
    {
      "participant_id": "sub-ATL002",
      "file1_counts": {
        "TPL": 0,
        "PCT": 0,
        "GFTA": 3,
        "PLSS": 0,
        "CELFS": 0,
        "CELFT": 0,
        "Other": 15
      },
      "file2_counts": {
        "TPL": 0,
        "PCT": 0,
        "GFTA": 3,
        "PLSS": 0,
        "CELFS": 0,
        "CELFT": 0,
        "Other": 18
      },
      "differences": ["Other"]
    }
  ]
}
```

### Output Sections

1. **comparison_summary**: Overview statistics of the comparison
2. **file1_only**: List of participant IDs present only in the first file
3. **file2_only**: List of participant IDs present only in the second file
4. **mismatched_counts**: Detailed information about participants with different count values

## Examples

### Running the Usage Example

```bash
python usage_example.py
```

This will create sample CSV files, run a comparison, and demonstrate the tool's functionality.

### Real-world Example

```bash
# Compare two sharepoint count files
python csv_comparison.py sharepoint_wav_count_old.csv sharepoint_wav_count_new.csv -o sync_report.json --pretty
```

## Error Handling

The tool handles various error conditions:

- **Missing files**: Clear error message if input files don't exist
- **Invalid CSV format**: Reports missing required columns
- **Invalid data**: Warns about non-numeric values in count columns (treats as 0)
- **Empty participant IDs**: Skips rows with empty ParticipantID values

## Use Cases

1. **Data Synchronization**: Verify that data transfers between systems are complete and accurate
2. **Quality Assurance**: Check for discrepancies between different data collection rounds
3. **Backup Verification**: Ensure backup files match original data
4. **Dataset Merging**: Identify conflicts before merging datasets
5. **Audit Trail**: Generate reports for data management compliance

## Command Line Options

```
usage: csv_comparison.py [-h] [-o OUTPUT] [--pretty] file1 file2

Compare two CSV files with participant data and generate JSON difference report

positional arguments:
  file1                 First CSV file path
  file2                 Second CSV file path

optional arguments:
  -h, --help            show this help message and exit
  -o OUTPUT, --output OUTPUT
                        Output JSON file path (default: print to stdout)
  --pretty              Pretty print JSON output
```

## Exit Codes

- `0`: Success
- `1`: Error (file not found, invalid format, etc.)

## Notes

- Participant IDs are compared as case-sensitive strings
- Count values must be integers (non-numeric values are treated as 0 with a warning)
- Output is sorted alphabetically by participant ID for consistency
- The tool preserves the full path information in the summary for reference
