#!/usr/bin/env python3
"""
Test script for the CSV comparison tool.
"""

import os
import sys
import tempfile
import json
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from csv_comparison import compare_csv_files


def test_csv_comparison():
    """Test the CSV comparison functionality."""
    
    # Create temporary test files
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Test file 1
        file1_content = """ParticipantID,TPL,PCT,GFTA,PLSS,CELFS,CELFT,Other
sub-ATL001,0,0,0,0,0,0,4
sub-ATL002,0,0,3,0,0,0,15
sub-ATL003,0,0,4,0,0,0,27
sub-BLT001,0,0,0,0,0,0,4
sub-COMMON,1,2,3,4,5,6,7
"""
        
        # Test file 2
        file2_content = """ParticipantID,TPL,PCT,GFTA,PLSS,CELFS,CELFT,Other
sub-ATL001,0,0,0,0,0,0,4
sub-ATL002,0,0,3,0,0,0,18
sub-ATL004,0,0,5,0,0,0,20
sub-CHI001,0,0,4,0,0,0,24
sub-COMMON,1,2,4,4,5,6,8
"""
        
        file1_path = temp_path / "test1.csv"
        file2_path = temp_path / "test2.csv"
        
        file1_path.write_text(file1_content)
        file2_path.write_text(file2_content)
        
        # Run comparison
        result = compare_csv_files(str(file1_path), str(file2_path))
        
        # Verify results
        print("Testing CSV comparison...")
        
        # Test file1_only
        expected_file1_only = ["sub-ATL003", "sub-BLT001"]
        assert result["file1_only"] == expected_file1_only, f"Expected {expected_file1_only}, got {result['file1_only']}"
        print("✓ file1_only participants identified correctly")
        
        # Test file2_only
        expected_file2_only = ["sub-ATL004", "sub-CHI001"]
        assert result["file2_only"] == expected_file2_only, f"Expected {expected_file2_only}, got {result['file2_only']}"
        print("✓ file2_only participants identified correctly")
        
        # Test mismatched counts
        mismatches = result["mismatched_counts"]
        assert len(mismatches) == 2, f"Expected 2 mismatches, got {len(mismatches)}"
        
        # Check ATL002 mismatch (Other: 15 vs 18)
        atl002_mismatch = next((m for m in mismatches if m["participant_id"] == "sub-ATL002"), None)
        assert atl002_mismatch is not None, "sub-ATL002 mismatch not found"
        assert "Other" in atl002_mismatch["differences"], "Other column difference not detected for sub-ATL002"
        assert atl002_mismatch["file1_counts"]["Other"] == 15, "Incorrect file1 Other count for sub-ATL002"
        assert atl002_mismatch["file2_counts"]["Other"] == 18, "Incorrect file2 Other count for sub-ATL002"
        print("✓ sub-ATL002 mismatch detected correctly")
        
        # Check COMMON mismatch (GFTA: 3 vs 4, Other: 7 vs 8)
        common_mismatch = next((m for m in mismatches if m["participant_id"] == "sub-COMMON"), None)
        assert common_mismatch is not None, "sub-COMMON mismatch not found"
        expected_diffs = ["GFTA", "Other"]
        assert set(common_mismatch["differences"]) == set(expected_diffs), f"Expected {expected_diffs}, got {common_mismatch['differences']}"
        print("✓ sub-COMMON mismatch detected correctly")
        
        # Test summary
        summary = result["comparison_summary"]
        assert summary["total_participants_file1"] == 5, "Incorrect file1 participant count"
        assert summary["total_participants_file2"] == 5, "Incorrect file2 participant count"
        assert summary["common_participants"] == 3, "Incorrect common participant count"
        assert summary["file1_only_count"] == 2, "Incorrect file1_only count"
        assert summary["file2_only_count"] == 2, "Incorrect file2_only count"
        assert summary["mismatched_count"] == 2, "Incorrect mismatch count"
        print("✓ Summary statistics correct")
        
        print("\n🎉 All tests passed!")
        
        # Print sample output
        print("\nSample JSON output:")
        print(json.dumps(result, indent=2))


def test_edge_cases():
    """Test edge cases and error conditions."""
    
    print("\nTesting edge cases...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Test empty files
        empty_file1 = temp_path / "empty1.csv"
        empty_file2 = temp_path / "empty2.csv"
        
        empty_file1.write_text("ParticipantID,TPL,PCT,GFTA,PLSS,CELFS,CELFT,Other\n")
        empty_file2.write_text("ParticipantID,TPL,PCT,GFTA,PLSS,CELFS,CELFT,Other\n")
        
        result = compare_csv_files(str(empty_file1), str(empty_file2))
        
        assert result["file1_only"] == [], "Empty files should have no file1_only participants"
        assert result["file2_only"] == [], "Empty files should have no file2_only participants"
        assert result["mismatched_counts"] == [], "Empty files should have no mismatches"
        print("✓ Empty files handled correctly")
        
        # Test identical files
        identical_content = """ParticipantID,TPL,PCT,GFTA,PLSS,CELFS,CELFT,Other
sub-TEST001,1,2,3,4,5,6,7
sub-TEST002,0,0,0,0,0,0,0
"""
        
        identical_file1 = temp_path / "identical1.csv"
        identical_file2 = temp_path / "identical2.csv"
        
        identical_file1.write_text(identical_content)
        identical_file2.write_text(identical_content)
        
        result = compare_csv_files(str(identical_file1), str(identical_file2))
        
        assert result["file1_only"] == [], "Identical files should have no file1_only participants"
        assert result["file2_only"] == [], "Identical files should have no file2_only participants"
        assert result["mismatched_counts"] == [], "Identical files should have no mismatches"
        assert result["comparison_summary"]["common_participants"] == 2, "Should have 2 common participants"
        print("✓ Identical files handled correctly")
        
        print("✓ All edge case tests passed!")


if __name__ == "__main__":
    test_csv_comparison()
    test_edge_cases()
    print("\n✅ All tests completed successfully!")
