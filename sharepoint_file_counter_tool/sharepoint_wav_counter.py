#!/usr/bin/env python3
"""
SharePoint WAV File Counter Script

This script connects to SharePoint and counts WAV files in subdirectories that start with 'sub-'
and generates a CSV report with participant IDs and task counts without downloading the files.

Usage:
    python3 sharepoint_wav_counter.py

Requirements:
    - Office365-REST-Python-Client
    - pandas
    - psutil

Install dependencies:
    pip install Office365-REST-Python-Client pandas psutil
"""

import os
import sys
import csv
import re
import time
import json
import gc
import psutil
from pathlib import Path
from collections import defaultdict, deque
import argparse

# SharePoint imports
from office365.sharepoint.client_context import ClientContext
from office365.runtime.auth.user_credential import UserCredential
from office365.runtime.client_request_exception import ClientRequestException


def authenticate_sharepoint(site_url):
    """
    Authenticate with SharePoint using username and password
    
    Args:
        site_url (str): SharePoint site URL
        
    Returns:
        ClientContext: Authenticated SharePoint context or None if failed
    """
    print("🔐 SharePoint Authentication")
    username = input("Enter your username: ")
    password = input("Enter your password: ")

    try:
        credentials = UserCredential(username, password)
        ctx = ClientContext(site_url).with_credentials(credentials)
        # Test the connection
        ctx.web.get()
        ctx.execute_query()
        print("✅ Authentication successful.")
        return ctx
    except ClientRequestException as e:
        if e.response.status_code == 401:
            print(f"❌ Authentication failed: Invalid username or password.")
        elif e.response.status_code == 403:
            print(f"❌ Authentication failed: You don't have permission to access this site.")
        else:
            print(f"❌ Authentication failed: {e}")
        return None
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        return None


def execute_with_retry(ctx, request, retries=3, delay=5):
    """
    Execute SharePoint request with retry logic
    
    Args:
        ctx: SharePoint context
        request: Lambda function containing the request
        retries: Number of retry attempts
        delay: Base delay between retries
        
    Returns:
        bool: True if successful, False if failed
    """
    for attempt in range(retries):
        try:
            request()
            return True
        except ClientRequestException as e:
            if e.response.status_code in [429, 503, 500, 502, 504]:
                wait_time = delay * (2 ** attempt) + (time.time() % 2)
                print(f"⚠️ Server error {e.response.status_code}. Retrying {attempt+1}/{retries} in {wait_time:.1f}s...")
                time.sleep(wait_time)
            else:
                print(f"❌ Error {e.response.status_code}: {e}")
                return False
        except Exception as e:
            print(f"❌ Unexpected error during request: {e}")
            return False
    print(f"❌ Request failed after {retries} retries.")
    return False


def extract_participant_id(file_path):
    """
    Extract participant ID from SharePoint file path by finding folder names that start with 'sub-'
    
    Args:
        file_path (str): SharePoint server relative URL path
        
    Returns:
        str: Participant ID (the sub- folder name) or None if not found
    """
    path_parts = file_path.split('/')
    for part in path_parts:
        if part.startswith('sub-'):
            return part
    return None


def extract_task_code(filename):
    """
    Extract task code from filename using pattern _task-<taskcode>
    
    Args:
        filename (str): Name of the file
        
    Returns:
        str: Task code or None if not found
    """
    pattern = r'_task-([A-Za-z0-9]+)'
    match = re.search(pattern, filename)
    if match:
        return match.group(1).upper()
    return None


def check_memory_usage(limit_percent=90):
    """
    Check if memory usage is above threshold
    
    Args:
        limit_percent (int): Memory usage threshold percentage
        
    Returns:
        bool: True if memory usage is acceptable, False if exceeded
    """
    memory = psutil.virtual_memory()
    if memory.percent > limit_percent:
        print(f"⚠️ Memory usage at {memory.percent}%, exceeding limit of {limit_percent}%")
        return False
    return True


def save_checkpoint(checkpoint_data, checkpoint_file):
    """
    Save checkpoint data to file
    
    Args:
        checkpoint_data (dict): Data to save
        checkpoint_file (str): Path to checkpoint file
    """
    try:
        data_to_save = checkpoint_data.copy()
        if 'folders_processed' in data_to_save:
            data_to_save['folders_processed'] = sorted(list(data_to_save['folders_processed']))

        with open(checkpoint_file, 'w') as f:
            json.dump(data_to_save, f, indent=2)
    except Exception as e:
        print(f"⚠️ Failed to save checkpoint {checkpoint_file}: {str(e)}")


def count_wav_files_in_sharepoint(ctx, start_folder_url, checkpoint_file="sharepoint_count_checkpoint.json", 
                                 max_runtime=None, memory_limit=90):
    """
    Count WAV files in SharePoint folders iteratively without downloading
    
    Args:
        ctx: SharePoint context
        start_folder_url (str): Starting folder URL
        checkpoint_file (str): Path to checkpoint file
        max_runtime (int): Maximum runtime in seconds
        memory_limit (int): Memory usage limit percentage
        
    Returns:
        dict: Nested dictionary with participant IDs as keys and task counts as values
    """
    start_time = time.time()
    
    # Define expected task codes
    expected_tasks = {'TPL', 'PCT', 'GFTA', 'PLSS', 'CELFS', 'CELFT'}
    
    # Initialize data structures
    participant_data = defaultdict(lambda: defaultdict(int))
    
    # Load checkpoint data
    checkpoints = {}
    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r') as f:
                checkpoints = json.load(f)
                checkpoints['folders_processed'] = set(checkpoints.get('folders_processed', []))
                checkpoints['participant_data'] = checkpoints.get('participant_data', {})
                # Restore participant data
                for pid, tasks in checkpoints['participant_data'].items():
                    for task, count in tasks.items():
                        participant_data[pid][task] = count
                print(f"📋 Loaded checkpoint. Processed {len(checkpoints['folders_processed'])} folders previously.")
        except Exception as e:
            print(f"⚠️ Error loading checkpoint: {e}. Starting fresh.")
            checkpoints = {'stats': {'wav_files_found': 0, 'folders_processed_count': 0, 'errors': 0}, 
                          'folders_processed': set(), 'participant_data': {}}
    
    if 'stats' not in checkpoints:
        checkpoints['stats'] = {'wav_files_found': 0, 'folders_processed_count': 0, 'errors': 0}
    if 'folders_processed' not in checkpoints:
        checkpoints['folders_processed'] = set()
    
    # Initialize folder queue
    folder_queue = deque()
    folder_queue.append(start_folder_url)
    print(f"🌳 Starting analysis from: {start_folder_url}")
    
    processed_in_session = 0
    
    while folder_queue:
        # Check memory and runtime limits
        if not check_memory_usage(memory_limit):
            print("💾 Saving checkpoint due to memory limit...")
            checkpoints['participant_data'] = dict(participant_data)
            save_checkpoint(checkpoints, checkpoint_file)
            print("🔄 Please restart the script to continue from checkpoint")
            sys.exit(0)
        
        if max_runtime and (time.time() - start_time) > max_runtime:
            print(f"⏱️ Maximum runtime ({max_runtime}s) reached.")
            checkpoints['participant_data'] = dict(participant_data)
            save_checkpoint(checkpoints, checkpoint_file)
            return participant_data
        
        folder_url = folder_queue.popleft()
        print(f"\n📂 Processing folder: {folder_url}")
        
        # Skip if already processed
        if folder_url in checkpoints['folders_processed']:
            print(f"⏭️ Skipping already processed folder: {folder_url}")
            continue
        
        try:
            # Get folder object
            current_folder = ctx.web.get_folder_by_server_relative_url(folder_url)
            if not execute_with_retry(ctx, lambda: ctx.load(current_folder, ["Name", "ServerRelativeUrl"])):
                print(f"⚠️ Failed to load folder {folder_url}, skipping")
                checkpoints['stats']['errors'] += 1
                continue
            if not execute_with_retry(ctx, lambda: ctx.execute_query()):
                print(f"⚠️ Failed to execute query for folder {folder_url}, skipping")
                checkpoints['stats']['errors'] += 1
                continue
            
            folder_name = current_folder.properties.get("Name", "N/A")
            print(f"📁 Folder name: {folder_name}")
            
            # Process files in current folder
            folder_files = current_folder.files
            if execute_with_retry(ctx, lambda: ctx.load(folder_files, ["Name", "ServerRelativeUrl"])) and \
               execute_with_retry(ctx, lambda: ctx.execute_query()):
                
                wav_files_in_folder = 0
                for file in folder_files:
                    file_name = file.properties.get("Name", "")
                    if file_name.lower().endswith(".wav"):
                        file_path = file.properties["ServerRelativeUrl"]
                        
                        # Extract participant ID and task code
                        participant_id = extract_participant_id(file_path)
                        task_code = extract_task_code(file_name)
                        
                        if participant_id and task_code:
                            # Only count files from folders that start with 'sub-'
                            path_has_sub_folder = any(part.startswith('sub-') for part in file_path.split('/'))
                            
                            if path_has_sub_folder:
                                if task_code in expected_tasks:
                                    participant_data[participant_id][task_code] += 1
                                else:
                                    participant_data[participant_id]['Other'] += 1
                                
                                wav_files_in_folder += 1
                                checkpoints['stats']['wav_files_found'] += 1
                                print(f"  📄 {participant_id}: {task_code} -> {file_name}")
                        elif file_name.lower().endswith(".wav"):
                            print(f"  ⚠️ Could not extract participant/task from: {file_name}")
                
                print(f"  Found {wav_files_in_folder} WAV files in this folder")
            
            # Process subfolders
            subfolders = current_folder.folders
            if execute_with_retry(ctx, lambda: ctx.load(subfolders, ["Name", "ServerRelativeUrl"])) and \
               execute_with_retry(ctx, lambda: ctx.execute_query()):
                
                subfolder_count = 0
                for subfolder in subfolders:
                    subfolder_url = subfolder.properties.get("ServerRelativeUrl")
                    subfolder_name = subfolder.properties.get("Name", "N/A")
                    
                    if subfolder_url:
                        folder_queue.append(subfolder_url)
                        subfolder_count += 1
                
                print(f"  Added {subfolder_count} subfolders to queue")
            
            # Mark folder as processed
            checkpoints['folders_processed'].add(folder_url)
            checkpoints['stats']['folders_processed_count'] = len(checkpoints['folders_processed'])
            processed_in_session += 1
            
            # Save checkpoint periodically
            save_freq = 1 if processed_in_session <= 10 else 5 if processed_in_session <= 50 else 10
            if processed_in_session % save_freq == 0:
                checkpoints['participant_data'] = dict(participant_data)
                save_checkpoint(checkpoints, checkpoint_file)
                stats = checkpoints['stats']
                print(f"📊 Progress: Folders={stats['folders_processed_count']}, WAV Files={stats['wav_files_found']}, Queue={len(folder_queue)}, Errors={stats['errors']}")
            
            # Clean up memory
            del current_folder
            del folder_files
            del subfolders
            gc.collect()
            
        except Exception as e:
            print(f"❌ Error processing folder {folder_url}: {e}")
            checkpoints['stats']['errors'] += 1
            continue
    
    print("\n✅ Analysis complete!")
    
    # Save final checkpoint
    checkpoints['participant_data'] = dict(participant_data)
    save_checkpoint(checkpoints, checkpoint_file)
    
    stats = checkpoints['stats']
    print(f"📊 Final Stats: Folders={stats['folders_processed_count']}, WAV Files={stats['wav_files_found']}, Errors={stats['errors']}")
    
    return participant_data


def generate_csv(participant_data, output_file):
    """
    Generate CSV output with participant data
    
    Args:
        participant_data (dict): Data structure with participant and task counts
        output_file (str): Output CSV filename
    """
    # Define column headers
    task_columns = ['TPL', 'PCT', 'GFTA', 'PLSS', 'CELFS', 'CELFT', 'Other']
    headers = ['ParticipantID'] + task_columns
    
    # Sort participants for consistent output
    sorted_participants = sorted(participant_data.keys())
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header
            writer.writerow(headers)
            
            # Write data rows
            for participant_id in sorted_participants:
                row = [participant_id]
                for task in task_columns:
                    count = participant_data[participant_id].get(task, 0)
                    row.append(count)
                writer.writerow(row)
        
        print(f"\n📄 CSV report generated: {output_file}")
        print(f"👥 Total participants: {len(sorted_participants)}")
        
        # Print summary
        print("\n📋 Summary:")
        for participant_id in sorted_participants:
            total_files = sum(participant_data[participant_id].values())
            print(f"  {participant_id}: {total_files} files")
            
    except Exception as e:
        print(f"❌ Error writing CSV file: {e}")


def main():
    """Main function to run the SharePoint WAV file counter"""
    parser = argparse.ArgumentParser(
        description='Count WAV files in SharePoint sub- directories and generate CSV report'
    )
    parser.add_argument(
        '--output', '-o',
        default='sharepoint_wav_count.csv',
        help='Output CSV filename (default: sharepoint_wav_count.csv)'
    )
    parser.add_argument(
        '--checkpoint', '-c',
        default='sharepoint_count_checkpoint.json',
        help='Checkpoint file path (default: sharepoint_count_checkpoint.json)'
    )
    parser.add_argument(
        '--memory-limit', '-m',
        type=int,
        default=85,
        help='Memory usage limit percentage (default: 85)'
    )
    parser.add_argument(
        '--max-runtime', '-t',
        type=int,
        help='Maximum runtime in seconds'
    )
    
    args = parser.parse_args()
    
    print("="*60)
    print("🔢 SharePoint WAV File Counter")
    print("="*60)
    
    # Get SharePoint connection details
    print("\n📋 Configuration:")
    site_url = input("Enter SharePoint site URL: ").strip()
    base_sharepoint_folder = input("Enter SharePoint base folder (e.g., /teams/YourTeam/Shared Documents): ").strip()
    start_sharepoint_relative_path = input("Enter relative path to dataset (e.g., Datasets/ds-SPROUT): ").strip()
    
    # Construct full start folder URL
    start_folder_url = f"{base_sharepoint_folder.rstrip('/')}/{start_sharepoint_relative_path.strip('/')}"
    
    print(f"\n🔗 SharePoint Details:")
    print(f"  Site URL: {site_url}")
    print(f"  Start Folder: {start_folder_url}")
    print(f"  Output File: {args.output}")
    print(f"  Checkpoint File: {args.checkpoint}")
    print(f"  Memory Limit: {args.memory_limit}%")
    if args.max_runtime:
        print(f"  Max Runtime: {args.max_runtime}s")
    print("-" * 60)
    
    # Authenticate with SharePoint
    ctx = authenticate_sharepoint(site_url)
    
    if not ctx:
        print("⚠️ Could not connect to SharePoint. Exiting.")
        sys.exit(1)
    
    try:
        print("\n🚀 Starting WAV file counting process...")
        gc.enable()
        
        # Count WAV files
        participant_data = count_wav_files_in_sharepoint(
            ctx,
            start_folder_url,
            checkpoint_file=args.checkpoint,
            max_runtime=args.max_runtime,
            memory_limit=args.memory_limit
        )
        
        if not participant_data:
            print("❌ No WAV files found or no data collected.")
            print("Please check that:")
            print("1. The SharePoint folder contains subdirectories starting with 'sub-'")
            print("2. Those subdirectories contain WAV files")
            print("3. The WAV files have task codes in format '_task-<taskcode>'")
            sys.exit(1)
        
        # Generate CSV output
        generate_csv(participant_data, args.output)
        
        print("\n🏁 Process completed successfully!")
        
    except SystemExit:
        print("\n🛑 Process exited gracefully due to limits.")
    except Exception as e:
        print(f"❌ Process encountered an error: {e}")
        # Save error details to checkpoint
        error_data = {"critical_error": str(e), "time": time.strftime("%Y-%m-%d %H:%M:%S")}
        error_file = f"{os.path.splitext(args.checkpoint)[0]}_error.json"
        save_checkpoint(error_data, error_file)
        print(f"Error details saved to {error_file}")
        sys.exit(1)


if __name__ == "__main__":
    main()
