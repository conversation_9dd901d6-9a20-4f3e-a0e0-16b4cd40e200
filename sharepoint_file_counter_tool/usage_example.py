#!/usr/bin/env python3
"""
Quick usage example for the SharePoint WAV file counter
"""

def show_help():
    """Show basic usage information"""
    print("SharePoint WAV File Counter - Quick Usage")
    print("=" * 50)
    print()
    print("BASIC USAGE:")
    print("  python3 sharepoint_wav_counter.py")
    print()
    print("WITH OPTIONS:")
    print("  python3 sharepoint_wav_counter.py --output my_report.csv")
    print("  python3 sharepoint_wav_counter.py --checkpoint my_checkpoint.json")
    print("  python3 sharepoint_wav_counter.py --memory-limit 90")
    print("  python3 sharepoint_wav_counter.py --max-runtime 3600")
    print()
    print("GET HELP:")
    print("  python3 sharepoint_wav_counter.py --help")
    print()
    print("RUN TEST:")
    print("  python3 test_sharepoint_counter.py")
    print()

def show_sharepoint_setup():
    """Show SharePoint configuration examples"""
    print("SHAREPOINT CONFIGURATION EXAMPLES:")
    print("=" * 50)
    print()
    print("Site URL Examples:")
    print("  https://yourorg.sharepoint.com/teams/ResearchTeam")
    print("  https://yourorg.sharepoint.com/sites/DataAnalysis")
    print()
    print("Base Folder Examples:")
    print("  /teams/ResearchTeam/Shared Documents")
    print("  /sites/DataAnalysis/Documents")
    print()
    print("Dataset Path Examples:")
    print("  Datasets/ds-SPROUT")
    print("  Research/AudioFiles/Participants")
    print("  Data/WAV_Files")
    print()

def show_expected_structure():
    """Show expected SharePoint directory structure"""
    print("EXPECTED SHAREPOINT STRUCTURE:")
    print("=" * 50)
    print("""
SharePoint Site/
├── Teams/YourTeam/Shared Documents/    # Base folder
│   └── Datasets/ds-SPROUT/             # Dataset path
│       ├── sub-ATL002/                 # Participant folder
│       │   ├── audio_task-TPL_001.wav
│       │   ├── session_task-PCT_001.wav
│       │   └── test_task-GFTA_001.wav
│       ├── sub-ATL003/
│       │   ├── data_task-PLSS_001.wav
│       │   └── eval_task-CELFS_001.wav
│       ├── other-folder/               # Ignored (no 'sub-')
│       │   └── ignored_file.wav
│       └── sub-BLT005/
│           └── nested/                 # Nested dirs supported
│               └── deep_task-CELFT.wav
    """)

def show_authentication_tips():
    """Show authentication and troubleshooting tips"""
    print("AUTHENTICATION & TROUBLESHOOTING:")
    print("=" * 50)
    print()
    print("BEFORE RUNNING:")
    print("  1. Test SharePoint access in your browser")
    print("  2. Verify you have read permissions")
    print("  3. Check the exact folder paths")
    print()
    print("AUTHENTICATION:")
    print("  • Use your organizational email as username")
    print("  • Use your regular password or app password")
    print("  • Ensure MFA settings allow app access")
    print()
    print("IF AUTHENTICATION FAILS:")
    print("  • Double-check the SharePoint site URL")
    print("  • Try accessing the site in browser first")
    print("  • Contact your IT admin about app permissions")
    print()
    print("PERFORMANCE TIPS:")
    print("  • Run during off-peak hours")
    print("  • Use stable network connection")
    print("  • Set memory limits for large datasets")
    print("  • Use checkpoints for resumable processing")
    print()

def show_output_format():
    """Show expected CSV output format"""
    print("OUTPUT CSV FORMAT:")
    print("=" * 50)
    print("""
ParticipantID,TPL,PCT,GFTA,PLSS,CELFS,CELFT,Other
sub-ATL002,3,3,3,3,3,0,0
sub-ATL003,4,8,4,4,4,3,0
sub-BLT005,4,4,4,4,0,0,0

Where:
- ParticipantID: Folder name that starts with 'sub-'
- TPL, PCT, etc.: Count of WAV files for each task type
- Other: Count of WAV files with unrecognized task codes
    """)

def show_installation():
    """Show installation instructions"""
    print("INSTALLATION:")
    print("=" * 50)
    print()
    print("1. INSTALL DEPENDENCIES:")
    print("   pip install -r requirements.txt")
    print()
    print("2. VERIFY INSTALLATION:")
    print("   python3 test_sharepoint_counter.py")
    print()
    print("3. RUN THE TOOL:")
    print("   python3 sharepoint_wav_counter.py")
    print()

if __name__ == "__main__":
    show_help()
    show_installation()
    show_sharepoint_setup()
    show_expected_structure()
    show_authentication_tips()
    show_output_format()
