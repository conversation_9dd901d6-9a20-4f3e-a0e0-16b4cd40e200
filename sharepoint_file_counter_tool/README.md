# SharePoint WAV File Counter Tool

This tool connects to SharePoint Online and counts WAV files in participant subdirectories without downloading them, generating CSV reports with task counts.

## Features

- **Direct SharePoint Integration**: Connects to SharePoint Online using Office365 REST API
- **Efficient Counting**: Counts files without downloading them (much faster than downloading)
- **Memory Management**: Built-in memory monitoring and checkpoint system
- **Resumable Operations**: Can resume from checkpoint if interrupted
- **Same Output Format**: Generates the same CSV format as the local file counter tool

## Expected Data Structure

The tool expects SharePoint folders with the following structure:

```
SharePoint Site/
└── Documents/
    └── Datasets/
        ├── sub-ATL002/          # Participant folder (starts with 'sub-')
        │   ├── audio_task-TPL_001.wav
        │   ├── session_task-PCT_001.wav
        │   └── test_task-GFTA_001.wav
        ├── sub-ATL003/
        │   ├── data_task-PLSS_001.wav
        │   └── eval_task-CELFS_001.wav
        └── other-folder/        # Ignored (doesn't start with 'sub-')
            └── ignored_file.wav
```

## Prerequisites

### Python Dependencies
Install required packages:
```bash
pip install -r requirements.txt
```

### SharePoint Access
- Valid SharePoint Online credentials
- Permission to access the target SharePoint site
- Access to the specific document library/folder

## Usage

### Basic Usage
```bash
python3 sharepoint_wav_counter.py
```

The script will prompt you for:
- SharePoint site URL
- Base SharePoint folder path
- Relative path to your dataset
- Authentication credentials (username/password)

### Command Line Options
```bash
# Specify output file
python3 sharepoint_wav_counter.py --output my_sharepoint_report.csv

# Use custom checkpoint file
python3 sharepoint_wav_counter.py --checkpoint my_checkpoint.json

# Set memory limit (default: 85%)
python3 sharepoint_wav_counter.py --memory-limit 90

# Set maximum runtime (in seconds)
python3 sharepoint_wav_counter.py --max-runtime 3600
```

### Example Input Session
```
Enter SharePoint site URL: https://yourorg.sharepoint.com/teams/YourTeam
Enter SharePoint base folder: /teams/YourTeam/Shared Documents
Enter relative path to dataset: Datasets/ds-SPROUT
Enter your username: <EMAIL>
Enter your password: [hidden]
```

## Output

### CSV Format
The tool generates a CSV file with the same format as the local file counter:

```csv
ParticipantID,TPL,PCT,GFTA,PLSS,CELFS,CELFT,Other
sub-ATL002,3,3,3,3,3,0,0
sub-ATL003,4,8,4,4,4,3,0
sub-ATL004,4,4,4,4,0,0,0
```

### Console Output
During execution, you'll see:
- Authentication status
- Folder processing progress
- File counts by participant and task
- Memory usage warnings
- Checkpoint saves
- Final statistics

## Task Code Recognition

The tool recognizes these predefined task codes:
- **TPL** - Template task
- **PCT** - Perception task  
- **GFTA** - Goldman-Fristoe Test of Articulation
- **PLSS** - Preschool Language Skills Survey
- **CELFS** - Clinical Evaluation of Language Fundamentals - Screening
- **CELFT** - Clinical Evaluation of Language Fundamentals - Test

Files with other task codes are counted under the **Other** column.

## File Naming Convention

WAV files should include task codes in their filename using the pattern:
`_task-<TASKCODE>`

**Examples:**
- ✅ `recording_task-TPL_001.wav`
- ✅ `session_task-PCT_final.wav` 
- ✅ `data_task-GFTA_baseline.wav`
- ❌ `file_without_task.wav` (will be skipped)

## Checkpoint System

### Automatic Checkpoints
The tool automatically saves progress to a checkpoint file:
- After processing every few folders
- When memory usage gets high
- Before the script exits

### Manual Resume
If the script is interrupted, restart it with the same parameters to resume from the last checkpoint.

### Checkpoint File Content
The checkpoint file contains:
- List of already processed folders
- Current participant data counts
- Processing statistics
- Timestamp information

## Memory Management

### Memory Monitoring
- Continuously monitors memory usage
- Warns when approaching limits
- Automatically saves checkpoint and exits gracefully if memory limit exceeded

### Memory Optimization
- Uses garbage collection
- Deletes large objects after processing
- Processes folders iteratively (not recursively)

## Error Handling

### Network Issues
- Automatic retry with exponential backoff
- Handles temporary SharePoint service issues
- Continues processing if individual folders fail

### Authentication Errors
- Clear error messages for common auth issues
- Supports username/password authentication
- Tests connection before starting main process

### File Access Issues
- Logs problematic files/folders
- Continues processing despite individual failures
- Maintains error count in statistics

## Performance Tips

### For Large Datasets
1. **Use memory limits**: Set appropriate `--memory-limit` 
2. **Set runtime limits**: Use `--max-runtime` for long operations
3. **Monitor progress**: Check checkpoint files periodically
4. **Run during off-hours**: Avoid peak SharePoint usage times

### For Faster Processing
1. **Stable network**: Use wired connection if possible
2. **Close other applications**: Free up system memory
3. **Restart if needed**: Resume from checkpoint for very large datasets

## Troubleshooting

### Common Issues

**Authentication Fails**
- Verify SharePoint site URL is correct
- Check username/password
- Ensure account has access to the site
- Try accessing the SharePoint site in a browser first

**Memory Errors**
- Reduce `--memory-limit` value
- Close other applications
- Process smaller chunks by setting `--max-runtime`

**Connection Timeouts**
- Check network connection
- Verify SharePoint site is accessible
- Try again during off-peak hours

**No Files Found**
- Verify the SharePoint folder path is correct
- Check that subdirectories start with 'sub-'
- Ensure WAV files have proper task codes in filenames

### Debug Mode
Add print statements or run the test script to verify logic:
```bash
python3 test_sharepoint_counter.py
```

## Comparison with Local Tool

| Feature | Local Tool | SharePoint Tool |
|---------|------------|-----------------|
| **Speed** | Fast | Slower (network dependent) |
| **Storage** | Requires local copies | No local storage needed |
| **Authentication** | None required | SharePoint credentials |
| **Network** | Not required | Required |
| **Resume capability** | No | Yes (checkpoints) |
| **Memory management** | Basic | Advanced |
| **Error handling** | Basic | Advanced |

## Security Notes

- Credentials are not stored permanently
- Use secure networks when possible
- Consider using app passwords instead of main passwords
- The tool only reads file metadata, not file contents

## Files in This Directory

- **`sharepoint_wav_counter.py`** - Main SharePoint counting script
- **`test_sharepoint_counter.py`** - Test script with mock data
- **`requirements.txt`** - Python dependencies
- **`README.md`** - This documentation file
