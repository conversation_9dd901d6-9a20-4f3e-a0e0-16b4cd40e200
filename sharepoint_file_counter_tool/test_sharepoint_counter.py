#!/usr/bin/env python3
"""
Test script for SharePoint WAV file counter
This creates a mock test to demonstrate the functionality
"""

import os
import csv
import tempfile
from collections import defaultdict

def create_sample_data():
    """Create sample data similar to what SharePoint would provide"""
    # Simulate SharePoint file structure
    sample_files = [
        # ATL participants
        ("/teams/site/Documents/Datasets/sub-ATL002/audio_task-TPL_001.wav", "audio_task-TPL_001.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/audio_task-TPL_002.wav", "audio_task-TPL_002.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/audio_task-TPL_003.wav", "audio_task-TPL_003.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/session_task-PCT_001.wav", "session_task-PCT_001.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/session_task-PCT_002.wav", "session_task-PCT_002.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/session_task-PCT_003.wav", "session_task-PCT_003.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/test_task-GFTA_001.wav", "test_task-GFTA_001.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/test_task-GFTA_002.wav", "test_task-GFTA_002.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/test_task-GFTA_003.wav", "test_task-GFTA_003.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/lang_task-PLSS_001.wav", "lang_task-PLSS_001.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/lang_task-PLSS_002.wav", "lang_task-PLSS_002.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/lang_task-PLSS_003.wav", "lang_task-PLSS_003.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/eval_task-CELFS_001.wav", "eval_task-CELFS_001.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/eval_task-CELFS_002.wav", "eval_task-CELFS_002.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL002/eval_task-CELFS_003.wav", "eval_task-CELFS_003.wav"),
        
        # ATL003 with different counts
        ("/teams/site/Documents/Datasets/sub-ATL003/audio_task-TPL_001.wav", "audio_task-TPL_001.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/audio_task-TPL_002.wav", "audio_task-TPL_002.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/audio_task-TPL_003.wav", "audio_task-TPL_003.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/audio_task-TPL_004.wav", "audio_task-TPL_004.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/session_task-PCT_001.wav", "session_task-PCT_001.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/session_task-PCT_002.wav", "session_task-PCT_002.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/session_task-PCT_003.wav", "session_task-PCT_003.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/session_task-PCT_004.wav", "session_task-PCT_004.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/session_task-PCT_005.wav", "session_task-PCT_005.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/session_task-PCT_006.wav", "session_task-PCT_006.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/session_task-PCT_007.wav", "session_task-PCT_007.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/session_task-PCT_008.wav", "session_task-PCT_008.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/test_task-GFTA_001.wav", "test_task-GFTA_001.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/test_task-GFTA_002.wav", "test_task-GFTA_002.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/test_task-GFTA_003.wav", "test_task-GFTA_003.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/test_task-GFTA_004.wav", "test_task-GFTA_004.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/lang_task-PLSS_001.wav", "lang_task-PLSS_001.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/lang_task-PLSS_002.wav", "lang_task-PLSS_002.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/lang_task-PLSS_003.wav", "lang_task-PLSS_003.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/lang_task-PLSS_004.wav", "lang_task-PLSS_004.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/eval_task-CELFS_001.wav", "eval_task-CELFS_001.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/eval_task-CELFS_002.wav", "eval_task-CELFS_002.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/eval_task-CELFS_003.wav", "eval_task-CELFS_003.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/eval_task-CELFS_004.wav", "eval_task-CELFS_004.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/eval_task-CELFT_001.wav", "eval_task-CELFT_001.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/eval_task-CELFT_002.wav", "eval_task-CELFT_002.wav"),
        ("/teams/site/Documents/Datasets/sub-ATL003/eval_task-CELFT_003.wav", "eval_task-CELFT_003.wav"),
        
        # Files to ignore (not in sub- folders)
        ("/teams/site/Documents/Datasets/other-folder/ignored_task-TPL.wav", "ignored_task-TPL.wav"),
        ("/teams/site/Documents/Datasets/regular-folder/also_ignored_task-PCT.wav", "also_ignored_task-PCT.wav"),
        
        # File without task code (should be skipped)
        ("/teams/site/Documents/Datasets/sub-ATL002/no_task_code.wav", "no_task_code.wav")
    ]
    
    return sample_files


def extract_participant_id(file_path):
    """Extract participant ID from file path"""
    path_parts = file_path.split('/')
    for part in path_parts:
        if part.startswith('sub-'):
            return part
    return None


def extract_task_code(filename):
    """Extract task code from filename"""
    import re
    pattern = r'_task-([A-Za-z0-9]+)'
    match = re.search(pattern, filename)
    if match:
        return match.group(1).upper()
    return None


def test_sharepoint_counter():
    """Test the SharePoint WAV file counting logic"""
    print("SharePoint WAV File Counter - Test Script")
    print("="*50)
    
    # Expected task codes
    expected_tasks = {'TPL', 'PCT', 'GFTA', 'PLSS', 'CELFS', 'CELFT'}
    
    # Initialize data structure
    participant_data = defaultdict(lambda: defaultdict(int))
    
    # Get sample data
    sample_files = create_sample_data()
    
    print(f"Processing {len(sample_files)} sample files...")
    
    # Process each file
    processed_files = 0
    skipped_files = 0
    
    for file_path, filename in sample_files:
        # Only process files from sub- folders
        path_has_sub_folder = any(part.startswith('sub-') for part in file_path.split('/'))
        
        if not path_has_sub_folder:
            print(f"⏭️ Skipping file not in sub- folder: {filename}")
            skipped_files += 1
            continue
        
        # Extract participant ID and task code
        participant_id = extract_participant_id(file_path)
        task_code = extract_task_code(filename)
        
        if participant_id and task_code:
            if task_code in expected_tasks:
                participant_data[participant_id][task_code] += 1
            else:
                participant_data[participant_id]['Other'] += 1
            
            print(f"  📄 {participant_id}: {task_code} -> {filename}")
            processed_files += 1
        elif filename.endswith('.wav'):
            print(f"  ⚠️ Could not extract participant/task from: {filename}")
            skipped_files += 1
    
    print(f"\nProcessed: {processed_files} files")
    print(f"Skipped: {skipped_files} files")
    
    # Generate test CSV
    output_file = "test_sharepoint_output.csv"
    generate_test_csv(participant_data, output_file)
    
    return participant_data


def generate_test_csv(participant_data, output_file):
    """Generate CSV output for test"""
    task_columns = ['TPL', 'PCT', 'GFTA', 'PLSS', 'CELFS', 'CELFT', 'Other']
    headers = ['ParticipantID'] + task_columns
    
    sorted_participants = sorted(participant_data.keys())
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header
            writer.writerow(headers)
            
            # Write data rows
            for participant_id in sorted_participants:
                row = [participant_id]
                for task in task_columns:
                    count = participant_data[participant_id].get(task, 0)
                    row.append(count)
                writer.writerow(row)
        
        print(f"\n📄 Test CSV generated: {output_file}")
        print(f"👥 Total participants: {len(sorted_participants)}")
        
        # Show CSV content
        print("\n📋 Generated CSV content:")
        print("-" * 40)
        with open(output_file, 'r') as f:
            print(f.read())
        
        # Print summary
        print("📊 Summary:")
        for participant_id in sorted_participants:
            total_files = sum(participant_data[participant_id].values())
            print(f"  {participant_id}: {total_files} files")
            
    except Exception as e:
        print(f"❌ Error writing CSV file: {e}")


def main():
    """Main test function"""
    try:
        test_sharepoint_counter()
        print("\n✅ Test completed successfully!")
        
        # Cleanup
        test_file = "test_sharepoint_output.csv"
        if os.path.exists(test_file):
            response = input(f"\nRemove test file {test_file}? (y/n): ").lower().strip()
            if response in ['y', 'yes']:
                os.remove(test_file)
                print(f"🧹 Removed {test_file}")
            else:
                print(f"📁 Kept {test_file}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    main()
