#!/usr/bin/env python3
"""
Usage Example for Empty Beh Cleanup Script

This script demonstrates how to use the empty_beh_cleanup.py script
with various scenarios and provides example usage patterns.
"""

import os
import tempfile
from pathlib import Path
import subprocess
import sys


def create_sample_cleanup_structure():
    """
    Create a sample directory structure for cleanup demonstration
    
    Returns:
        str: Path to the sample directory
    """
    # Create temporary directory
    temp_dir = tempfile.mkdtemp(prefix="beh_cleanup_demo_")
    sample_dir = Path(temp_dir) / "organized_data"
    
    # Create sample structure with various scenarios
    sample_scenarios = [
        # Scenario 1: Participant with empty beh folder (should be deleted)
        "sub-001/beh/",
        
        # Scenario 2: Participant with non-empty beh folder (should remain)
        "sub-002/beh/important_video.mp4",
        "sub-002/beh/analysis_notes.txt",
        
        # Scenario 3: Another participant with empty beh folder (should be deleted)
        "sub-003/beh/",
        
        # Scenario 4: Participant with empty beh but other files (should remain)
        "sub-004/beh/",
        "sub-004/readme.txt",
        "sub-004/logs/processing.log",
        
        # Scenario 5: Completely empty participant folder (should be deleted)
        "sub-005/",
        
        # Scenario 6: Participant with files but no beh folder (should remain)
        "sub-006/session_data/raw_file.dat",
        
        # Scenario 7: Participant with empty beh folder (should be deleted)
        "sub-007/beh/",
        
        # Scenario 8: Non-participant folder (should be ignored)
        "other_data/beh/some_file.txt",
        
        # Scenario 9: Participant with nested empty directories (should remain)
        "sub-008/beh/",
        "sub-008/analysis/empty_subfolder/",
        
        # Scenario 10: Participant with active data (should remain)
        "sub-009/beh/sub-009_task-rest_run-01.mp4",
        "sub-009/beh/sub-009_task-nback_run-02.avi",
    ]
    
    print(f"📁 Creating sample cleanup structure in: {sample_dir}")
    
    for scenario in sample_scenarios:
        full_path = sample_dir / scenario
        
        if scenario.endswith('/'):
            # Create directory
            full_path.mkdir(parents=True, exist_ok=True)
        else:
            # Create file
            full_path.parent.mkdir(parents=True, exist_ok=True)
            with open(full_path, 'w') as f:
                f.write(f"Sample file: {full_path.name}\n")
                f.write(f"Original location: {full_path}\n")
    
    print(f"✅ Sample cleanup structure created!")
    print(f"   Directory: {sample_dir}")
    
    return str(sample_dir)


def demonstrate_cleanup_usage():
    """
    Demonstrate various usage scenarios of the empty beh cleanup script
    """
    print("🧹 Empty Beh Cleanup Script - Usage Examples")
    print("=" * 60)
    
    # Create sample data
    sample_dir = create_sample_cleanup_structure()
    
    print(f"\n📋 Example 1: Dry run (preview mode)")
    print(f"Command: python3 empty_beh_cleanup.py {sample_dir} --dry-run")
    print(f"This will show what would be deleted without actually deleting anything")
    
    print(f"\n📋 Example 2: Actual cleanup")
    print(f"Command: python3 empty_beh_cleanup.py {sample_dir}")
    print(f"This will perform the actual cleanup with default log file")
    
    print(f"\n📋 Example 3: Cleanup with custom log file")
    print(f"Command: python3 empty_beh_cleanup.py {sample_dir} custom_cleanup.log")
    print(f"This will use 'custom_cleanup.log' instead of the default log file")
    
    print(f"\n📋 Example 4: Dry run with custom log")
    print(f"Command: python3 empty_beh_cleanup.py {sample_dir} --dry-run custom_log.txt")
    print(f"This will preview cleanup with custom log file")
    
    print(f"\n📂 Current directory structure:")
    for root, dirs, files in os.walk(sample_dir):
        level = root.replace(sample_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")
    
    # Run dry-run first
    script_path = Path(__file__).parent / "empty_beh_cleanup.py"
    log_file = Path(sample_dir).parent / "demo_cleanup_log.txt"
    
    print(f"\n🔍 Running dry-run cleanup...")
    try:
        result = subprocess.run([
            sys.executable, str(script_path), 
            sample_dir, str(log_file), "--dry-run"
        ], capture_output=True, text=True, check=True)
        
        print("✅ Dry-run completed successfully!")
        print(f"Output:\n{result.stdout}")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running dry-run: {e}")
        print(f"Error output: {e.stderr}")
        return
    
    # Ask user if they want to proceed with actual cleanup
    print(f"\n❓ The dry-run showed what would be deleted.")
    print(f"Now running actual cleanup...")
    
    # Run actual cleanup
    try:
        result = subprocess.run([
            sys.executable, str(script_path), 
            sample_dir, str(log_file)
        ], capture_output=True, text=True, check=True)
        
        print("✅ Actual cleanup completed successfully!")
        print(f"Output:\n{result.stdout}")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running cleanup: {e}")
        print(f"Error output: {e.stderr}")
        return
    
    print(f"\n📂 Final directory structure:")
    for root, dirs, files in os.walk(sample_dir):
        level = root.replace(sample_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")
    
    # Show log file contents
    if os.path.exists(log_file):
        print(f"\n📝 Log file contents ({log_file}):")
        with open(log_file, 'r') as f:
            for line in f:
                print(f"   {line.strip()}")
    
    # Clean up
    print(f"\n🧹 Demo files created in: {Path(sample_dir).parent}")
    print(f"You can delete this directory when done: rm -rf {Path(sample_dir).parent}")


def show_cleanup_help():
    """Show detailed help and usage information for cleanup script"""
    print("🧹 Empty Beh Cleanup Script - Detailed Usage Guide")
    print("=" * 60)
    
    print("""
📖 DESCRIPTION:
   This tool finds empty 'beh' folders under participant directories and safely
   deletes the corresponding participant folder only if it contains only the
   empty beh folder or is completely empty.
   
🎯 WHAT IT DELETES:
   - Participant folders that contain only an empty 'beh' directory
   - Completely empty participant folders (no files or subdirectories)
   
🛡️  SAFETY FEATURES:
   - Only deletes participant folders matching 'sub-XXX' pattern
   - Never deletes folders containing other files or non-empty subdirectories
   - Comprehensive logging of all operations
   - Dry-run mode for safe preview
   - Safety checks before each deletion
   
🔍 PARTICIPANT FOLDER DETECTION:
   The script only considers folders that:
   1. Start with 'sub-' (e.g., sub-001, sub-123)
   2. Have additional characters after 'sub-' (minimum length 5)
   
📁 DELETION CRITERIA:
   A participant folder will be deleted if:
   1. It contains only an empty 'beh' directory, OR
   2. It is completely empty (no files or subdirectories)
   
   A participant folder will NOT be deleted if:
   1. The 'beh' directory contains any files
   2. There are other files or directories besides 'beh'
   3. It doesn't match the participant naming pattern
   
⚠️  IMPORTANT NOTES:
   - Always run with --dry-run first to preview what will be deleted
   - Deleted folders cannot be recovered
   - All operations are logged for audit purposes
   - The script is conservative - when in doubt, it won't delete
   
💻 COMMAND LINE USAGE:
   python3 empty_beh_cleanup.py <root_directory> [log_file] [--dry-run]
   
   Arguments:
   - root_directory: Directory to search for empty beh folders (required)
   - log_file: Custom log filename (optional, default: empty_beh_cleanup_log.txt)
   - --dry-run: Preview mode without actual deletion (optional)
   
📚 EXAMPLES:
   
   Preview what would be deleted:
   python3 empty_beh_cleanup.py /path/to/organized/data --dry-run
   
   Perform actual cleanup:
   python3 empty_beh_cleanup.py /path/to/organized/data
   
   With custom log file:
   python3 empty_beh_cleanup.py /path/to/data custom_cleanup.log
   
   Dry run with custom log:
   python3 empty_beh_cleanup.py /path/to/data --dry-run preview.log
   
🔧 TROUBLESHOOTING:
   - Check the log file for detailed information about all operations
   - Use --dry-run to understand what the script will do
   - Ensure you have write permissions to the target directory
   - Verify that participant folders follow expected naming conventions
   
🚀 WORKFLOW RECOMMENDATION:
   1. Always run with --dry-run first to preview changes
   2. Review the log file and output carefully
   3. If satisfied with the preview, run without --dry-run
   4. Keep the log file for your records
    """)


def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_cleanup_help()
    else:
        demonstrate_cleanup_usage()


if __name__ == "__main__":
    main()
