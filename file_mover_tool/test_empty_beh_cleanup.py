#!/usr/bin/env python3
"""
Test script for empty_beh_cleanup.py

This script creates a test environment with sample directory structures
and tests the empty beh folder cleanup functionality.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the current directory to the path to import the empty_beh_cleanup module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from empty_beh_cleanup import (
    is_participant_folder,
    is_empty_directory,
    find_empty_beh_folders,
    check_participant_folder_safety,
    cleanup_empty_beh_folders
)


def create_test_structure(base_dir):
    """
    Create a test directory structure with various scenarios
    
    Args:
        base_dir (str): Base directory for the test structure
    """
    # Test scenarios to create
    test_scenarios = [
        # Scenario 1: Empty beh folder - should be deleted
        "sub-001/beh/",
        
        # Scenario 2: Beh folder with files - should NOT be deleted
        "sub-002/beh/video.mp4",
        
        # Scenario 3: Empty beh folder - should be deleted
        "sub-003/beh/",
        
        # Scenario 4: Participant folder with beh and other files - should NOT be deleted
        "sub-004/beh/",
        "sub-004/other_file.txt",
        
        # Scenario 5: Participant folder with non-empty beh - should NOT be deleted
        "sub-005/beh/important_video.avi",
        
        # Scenario 6: Participant folder with only empty beh - should be deleted
        "sub-006/beh/",
        
        # Scenario 7: Non-participant folder - should be ignored
        "not_a_participant/beh/",
        
        # Scenario 8: Participant folder without beh folder - should be ignored
        "sub-007/other_folder/file.txt",
        
        # Scenario 9: Completely empty participant folder - should be deleted
        "sub-008/",
        
        # Scenario 10: Participant folder with nested empty directories - should NOT be deleted
        "sub-009/beh/",
        "sub-009/other_dir/nested_dir/",
    ]
    
    base_path = Path(base_dir)
    
    for scenario in test_scenarios:
        full_path = base_path / scenario
        
        if scenario.endswith('/'):
            # Create directory
            full_path.mkdir(parents=True, exist_ok=True)
        else:
            # Create file
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.touch()
    
    print(f"✅ Created test structure in: {base_dir}")
    return test_scenarios


def test_helper_functions():
    """Test individual helper functions"""
    print("\n🧪 Testing helper functions...")
    
    # Test is_participant_folder
    test_cases = [
        ("sub-001", True),
        ("sub-123", True),
        ("sub-", False),
        ("not_sub", False),
        ("sub", False),
        ("sub-456-extra", True),  # Should still be True
    ]
    
    print("Testing is_participant_folder:")
    for folder_name, expected in test_cases:
        result = is_participant_folder(folder_name)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{folder_name}' -> {result} (expected: {expected})")
    
    # Test is_empty_directory with a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        empty_dir = Path(temp_dir) / "empty"
        empty_dir.mkdir()
        
        non_empty_dir = Path(temp_dir) / "non_empty"
        non_empty_dir.mkdir()
        (non_empty_dir / "file.txt").touch()
        
        print("Testing is_empty_directory:")
        print(f"  ✅ Empty dir -> {is_empty_directory(str(empty_dir))} (expected: True)")
        print(f"  ✅ Non-empty dir -> {is_empty_directory(str(non_empty_dir))} (expected: False)")


def test_cleanup_functionality():
    """Test the complete cleanup functionality"""
    print("\n🧪 Testing cleanup functionality...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test structure
        create_test_structure(temp_dir)
        
        print(f"\n📂 Initial directory structure:")
        for root, dirs, files in os.walk(temp_dir):
            level = root.replace(temp_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                print(f"{subindent}{file}")
        
        # Find empty beh folders
        empty_beh_folders = find_empty_beh_folders(temp_dir)
        print(f"\n📊 Found {len(empty_beh_folders)} empty beh folders:")
        for participant_folder, beh_folder in empty_beh_folders:
            participant_name = Path(participant_folder).name
            print(f"  - {participant_name}")
        
        # Test safety checks
        print(f"\n🛡️  Testing safety checks:")
        for participant_folder, beh_folder in empty_beh_folders:
            participant_name = Path(participant_folder).name
            is_safe = check_participant_folder_safety(participant_folder)
            status = "✅ SAFE" if is_safe else "❌ UNSAFE"
            print(f"  {status} {participant_name}")
        
        # Perform dry run cleanup
        print(f"\n🔍 Performing dry run cleanup...")
        dry_run_stats = cleanup_empty_beh_folders(temp_dir, dry_run=True)
        
        print(f"Dry run results:")
        print(f"  Empty beh folders found: {dry_run_stats['empty_beh_found']}")
        print(f"  Would delete: {dry_run_stats['folders_deleted']}")
        print(f"  Would fail: {dry_run_stats['deletion_failed']}")
        print(f"  Unsafe (skipped): {dry_run_stats['unsafe_deletions']}")
        
        # Perform actual cleanup
        print(f"\n🧹 Performing actual cleanup...")
        actual_stats = cleanup_empty_beh_folders(temp_dir, dry_run=False)
        
        print(f"Actual cleanup results:")
        print(f"  Empty beh folders found: {actual_stats['empty_beh_found']}")
        print(f"  Successfully deleted: {actual_stats['folders_deleted']}")
        print(f"  Failed deletions: {actual_stats['deletion_failed']}")
        print(f"  Unsafe deletions skipped: {actual_stats['unsafe_deletions']}")
        
        print(f"\n📂 Final directory structure:")
        for root, dirs, files in os.walk(temp_dir):
            level = root.replace(temp_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                print(f"{subindent}{file}")
        
        # Verify expected results
        remaining_participants = [d for d in os.listdir(temp_dir) 
                                if os.path.isdir(os.path.join(temp_dir, d)) 
                                and is_participant_folder(d)]
        
        print(f"\n📊 Remaining participant folders: {remaining_participants}")
        
        # Expected to remain: sub-002, sub-004, sub-005, sub-007, sub-009
        # Expected to be deleted: sub-001, sub-003, sub-006, sub-008
        expected_remaining = {'sub-002', 'sub-004', 'sub-005', 'sub-007', 'sub-009'}
        actual_remaining = set(remaining_participants)
        
        if expected_remaining == actual_remaining:
            print("✅ Cleanup test completed successfully!")
        else:
            print("❌ Cleanup test failed!")
            print(f"   Expected remaining: {expected_remaining}")
            print(f"   Actually remaining: {actual_remaining}")
            print(f"   Missing: {expected_remaining - actual_remaining}")
            print(f"   Extra: {actual_remaining - expected_remaining}")


def main():
    """Run all tests"""
    print("🧪 Running Empty Beh Cleanup Tests")
    print("=" * 50)
    
    test_helper_functions()
    test_cleanup_functionality()
    
    print("\n🏁 All tests completed!")


if __name__ == "__main__":
    main()
