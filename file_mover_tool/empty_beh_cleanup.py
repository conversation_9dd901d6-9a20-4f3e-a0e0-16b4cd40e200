#!/usr/bin/env python3
"""
Empty Beh Folder Cleanup Script

This script looks for empty 'beh' folders under a given root directory and deletes
the corresponding participant folder only if the beh folder is empty. This is useful
for cleaning up directory structures after file organization operations.

Usage:
    python3 empty_beh_cleanup.py <root_directory> [log_file] [--dry-run]

Arguments:
    root_directory: Path to the root directory to search for empty beh folders
    log_file: Optional log filename (default: empty_beh_cleanup_log.txt)
    --dry-run: Preview what would be deleted without actually deleting anything

Safety Features:
    - Only deletes if beh folder is completely empty
    - Only deletes participant folders that match sub-XXX pattern
    - Comprehensive logging of all operations
    - Dry-run mode for safe preview
"""

import os
import sys
import shutil
import logging
from pathlib import Path
from datetime import datetime
import argparse


def setup_logging(log_file):
    """
    Set up logging configuration
    
    Args:
        log_file (str): Path to the log file
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )


def is_participant_folder(folder_name):
    """
    Check if a folder name matches the participant ID pattern (sub-XXX)
    
    Args:
        folder_name (str): Name of the folder
        
    Returns:
        bool: True if it matches participant pattern, False otherwise
    """
    return folder_name.startswith('sub-') and len(folder_name) > 4


def is_empty_directory(directory_path):
    """
    Check if a directory is completely empty
    
    Args:
        directory_path (str): Path to the directory
        
    Returns:
        bool: True if directory is empty, False otherwise
    """
    try:
        return len(os.listdir(directory_path)) == 0
    except (OSError, FileNotFoundError):
        return False


def find_empty_beh_folders(root_directory):
    """
    Find all empty beh folders under participant directories, and also completely empty participant folders
    
    Args:
        root_directory (str): Root directory to search
        
    Returns:
        list: List of tuples (participant_folder_path, beh_folder_path or None for completely empty)
    """
    empty_beh_folders = []
    root_path = Path(root_directory)
    
    if not root_path.exists():
        logging.error(f"Root directory does not exist: {root_directory}")
        return empty_beh_folders
    
    logging.info(f"🔍 Searching for empty beh folders in: {root_directory}")
    
    # Look for participant folders
    for item in root_path.iterdir():
        if item.is_dir() and is_participant_folder(item.name):
            participant_folder = item
            beh_folder = participant_folder / "beh"
            
            # Check if participant folder is completely empty
            if is_empty_directory(str(participant_folder)):
                empty_beh_folders.append((str(participant_folder), None))
                logging.info(f"Found completely empty participant folder: {participant_folder}")
            # Check if beh folder exists and is empty
            elif beh_folder.exists() and beh_folder.is_dir():
                if is_empty_directory(str(beh_folder)):
                    empty_beh_folders.append((str(participant_folder), str(beh_folder)))
                    logging.info(f"Found empty beh folder: {beh_folder}")
                else:
                    logging.info(f"Beh folder not empty: {beh_folder}")
            else:
                logging.debug(f"No beh folder found in: {participant_folder}")
    
    logging.info(f"📊 Total empty beh folders/participant folders found: {len(empty_beh_folders)}")
    return empty_beh_folders


def check_participant_folder_safety(participant_folder):
    """
    Check if it's safe to delete the participant folder
    (only contains empty beh folder or is completely empty)
    
    Args:
        participant_folder (str): Path to the participant folder
        
    Returns:
        bool: True if safe to delete, False otherwise
    """
    try:
        participant_path = Path(participant_folder)
        contents = list(participant_path.iterdir())
        
        # If completely empty, safe to delete
        if len(contents) == 0:
            return True
        
        # If only contains 'beh' folder, check if beh is empty
        if len(contents) == 1 and contents[0].name == "beh" and contents[0].is_dir():
            return is_empty_directory(str(contents[0]))
        
        # If contains other files/folders, not safe to delete
        logging.warning(f"Participant folder contains other items: {participant_folder}")
        for item in contents:
            logging.warning(f"  - {item.name} ({'dir' if item.is_dir() else 'file'})")
        
        return False
        
    except Exception as e:
        logging.error(f"Error checking participant folder safety: {e}")
        return False


def delete_participant_folder(participant_folder, dry_run=False):
    """
    Delete a participant folder if it's safe to do so
    
    Args:
        participant_folder (str): Path to the participant folder
        dry_run (bool): If True, don't actually delete, just log what would be deleted
        
    Returns:
        bool: True if successful (or would be successful in dry-run), False if failed
    """
    try:
        if not check_participant_folder_safety(participant_folder):
            logging.warning(f"❌ Not safe to delete participant folder: {participant_folder}")
            return False
        
        if dry_run:
            logging.info(f"🔍 [DRY RUN] Would delete participant folder: {participant_folder}")
            return True
        else:
            shutil.rmtree(participant_folder)
            logging.info(f"✅ Successfully deleted participant folder: {participant_folder}")
            return True
            
    except Exception as e:
        logging.error(f"❌ Failed to delete participant folder {participant_folder}: {str(e)}")
        return False


def cleanup_empty_beh_folders(root_directory, dry_run=False):
    """
    Find and clean up empty beh folders and their participant directories
    
    Args:
        root_directory (str): Root directory to search
        dry_run (bool): If True, preview mode without actual deletion
        
    Returns:
        dict: Statistics about the cleanup operation
    """
    stats = {
        'empty_beh_found': 0,
        'folders_deleted': 0,
        'deletion_failed': 0,
        'unsafe_deletions': 0
    }
    
    # Find empty beh folders
    empty_beh_folders = find_empty_beh_folders(root_directory)
    stats['empty_beh_found'] = len(empty_beh_folders)
    
    if not empty_beh_folders:
        logging.info("No empty beh folders found.")
        return stats
    
    # Process each empty beh folder
    mode_text = "DRY RUN - " if dry_run else ""
    logging.info(f"🧹 {mode_text}Starting cleanup of {len(empty_beh_folders)} empty beh folders/participant folders...")
    
    for participant_folder, beh_folder in empty_beh_folders:
        participant_name = Path(participant_folder).name
        if beh_folder is None:
            logging.info(f"📁 Processing completely empty participant: {participant_name}")
        else:
            logging.info(f"📁 Processing participant with empty beh folder: {participant_name}")
        
        # Attempt to delete the participant folder
        if delete_participant_folder(participant_folder, dry_run):
            stats['folders_deleted'] += 1
        else:
            if check_participant_folder_safety(participant_folder):
                stats['deletion_failed'] += 1
            else:
                stats['unsafe_deletions'] += 1
    
    return stats


def print_summary(stats, dry_run=False):
    """
    Print a summary of the cleanup operation
    
    Args:
        stats (dict): Statistics from the cleanup operation
        dry_run (bool): Whether this was a dry run
    """
    mode_text = " (DRY RUN)" if dry_run else ""
    
    print("\n" + "="*60)
    print(f"🧹 EMPTY BEH FOLDER CLEANUP SUMMARY{mode_text}")
    print("="*60)
    print(f"Empty beh folders found: {stats['empty_beh_found']}")
    print(f"Participant folders deleted: {stats['folders_deleted']}")
    print(f"Failed deletions: {stats['deletion_failed']}")
    print(f"Unsafe deletions skipped: {stats['unsafe_deletions']}")
    print("="*60)
    
    if dry_run:
        print("🔍 This was a dry run - no files were actually deleted.")
        print("💡 Run without --dry-run to perform actual cleanup.")
    elif stats['deletion_failed'] > 0 or stats['unsafe_deletions'] > 0:
        print("⚠️  Some folders could not be deleted. Check the log for details.")
    elif stats['folders_deleted'] > 0:
        print("✅ Cleanup completed successfully!")
    else:
        print("ℹ️  No cleanup was needed.")


def main():
    """
    Main function to handle command line arguments and orchestrate the cleanup process
    """
    parser = argparse.ArgumentParser(
        description="Clean up empty beh folders and their participant directories",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python3 empty_beh_cleanup.py /path/to/organized/data
    python3 empty_beh_cleanup.py /path/to/data --dry-run
    python3 empty_beh_cleanup.py /path/to/data custom_log.txt
    python3 empty_beh_cleanup.py /path/to/data --dry-run custom_log.txt

Safety Notes:
    - Only deletes participant folders that contain only empty beh folders
    - Always use --dry-run first to preview what will be deleted
    - Participant folders must match sub-XXX pattern to be considered
        """
    )
    
    parser.add_argument(
        'root_directory',
        help='Root directory to search for empty beh folders'
    )
    
    parser.add_argument(
        'log_file',
        nargs='?',
        default='empty_beh_cleanup_log.txt',
        help='Log file for the operation (default: empty_beh_cleanup_log.txt)'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Preview what would be deleted without actually deleting anything'
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not os.path.exists(args.root_directory):
        print(f"❌ Error: Root directory does not exist: {args.root_directory}")
        sys.exit(1)
    
    # Set up logging
    setup_logging(args.log_file)
    
    mode_text = " (DRY RUN)" if args.dry_run else ""
    logging.info(f"🧹 Starting Empty Beh Folder Cleanup{mode_text}")
    logging.info(f"Root directory: {args.root_directory}")
    logging.info(f"Log file: {args.log_file}")
    
    if args.dry_run:
        logging.info("🔍 Running in DRY RUN mode - no files will be deleted")
    
    # Perform the cleanup
    stats = cleanup_empty_beh_folders(args.root_directory, args.dry_run)
    
    # Print summary
    print_summary(stats, args.dry_run)
    
    logging.info("🏁 Empty Beh Folder Cleanup completed")


if __name__ == "__main__":
    main()
