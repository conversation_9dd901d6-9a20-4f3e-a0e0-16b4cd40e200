# Video File Mover Tool

This tool provides two main utilities for organizing video files:

1. **Video File Mover** (`video_file_mover.py`) - Moves video files from a source directory structure to an organized target directory with the format `participant_id/beh/`
2. **Empty Beh Cleanup** (`empty_beh_cleanup.py`) - Cleans up empty `beh` folders and their corresponding participant directories

## Video File Mover

### Features

- **Recursive Search**: Goes through all subdirectories of the source directory
- **Video File Detection**: Supports multiple video formats (.mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .m4v, .3gp, .mpg, .mpeg, .m2v, .asf)
- **Participant ID Extraction**: Automatically detects participant IDs from folder names or filenames (pattern: `sub-XXX`)
- **Organized Structure**: Creates `participant_id/beh/` directory structure in target location
- **Move Operation**: Moves files (doesn't copy) to avoid duplication
- **Conflict Resolution**: Handles filename conflicts by adding timestamps
- **Comprehensive Logging**: Logs all operations and failures for troubleshooting
- **Error Handling**: Gracefully handles errors and continues processing

### Usage

#### Basic Usage
```bash
python3 video_file_mover.py <source_directory> <target_directory>
```

#### With Custom Log File
```bash
python3 video_file_mover.py <source_directory> <target_directory> <log_file>
```

#### Arguments
- `source_directory`: Path to the source directory to search for video files (required)
- `target_directory`: Path to the target directory where files will be moved (required)
- `log_file`: Optional log filename (default: `video_move_log.txt`)

## Empty Beh Cleanup

### Features

- **Safe Deletion**: Only deletes participant folders that contain only empty `beh` directories or are completely empty
- **Participant Pattern Matching**: Only considers folders matching `sub-XXX` pattern for deletion
- **Safety Checks**: Multiple safety checks before deletion to prevent accidental data loss
- **Dry-Run Mode**: Preview what would be deleted without actually deleting anything
- **Comprehensive Logging**: Detailed logging of all operations and safety checks
- **Conservative Approach**: When in doubt, the script won't delete anything

### Usage

#### Dry Run (Recommended First Step)
```bash
python3 empty_beh_cleanup.py <root_directory> --dry-run
```

#### Actual Cleanup
```bash
python3 empty_beh_cleanup.py <root_directory>
```

#### With Custom Log File
```bash
python3 empty_beh_cleanup.py <root_directory> <log_file>
```

#### Dry Run with Custom Log
```bash
python3 empty_beh_cleanup.py <root_directory> --dry-run <log_file>
```

#### Arguments
- `root_directory`: Path to the root directory to search for empty beh folders (required)
- `log_file`: Optional log filename (default: `empty_beh_cleanup_log.txt`)
- `--dry-run`: Preview what would be deleted without actually deleting anything (optional)

### What Gets Deleted
- Participant folders that contain only an empty `beh` directory
- Completely empty participant folders (no files or subdirectories)

### What Will NOT Be Deleted
- Participant folders with non-empty `beh` directories
- Participant folders containing other files or directories besides `beh`
- Folders that don't match the `sub-XXX` participant pattern
- Any folder when safety checks fail

## Examples

### Video File Mover Examples

#### Example 1: Basic Video File Moving
```bash
python3 video_file_mover.py /path/to/raw/recordings /path/to/organized/data
```

#### Example 2: Video Moving with Custom Log
```bash
python3 video_file_mover.py /raw/data /organized/data behavioral_move_log.txt
```

#### Example 3: Real-world Video Moving
```bash
python3 video_file_mover.py /Volumes/RawData/ParticipantRecordings /Volumes/ProcessedData/Behavioral
```

### Empty Beh Cleanup Examples

#### Example 1: Preview Cleanup (Recommended First)
```bash
python3 empty_beh_cleanup.py /path/to/organized/data --dry-run
```

#### Example 2: Actual Cleanup
```bash
python3 empty_beh_cleanup.py /path/to/organized/data
```

#### Example 3: Cleanup with Custom Log
```bash
python3 empty_beh_cleanup.py /path/to/data custom_cleanup.log
```

### Typical Workflow

1. **Organize video files:**
   ```bash
   python3 video_file_mover.py /raw/recordings /organized/behavioral
   ```

2. **Preview cleanup of empty folders:**
   ```bash
   python3 empty_beh_cleanup.py /organized/behavioral --dry-run
   ```

3. **Perform cleanup if satisfied with preview:**
   ```bash
   python3 empty_beh_cleanup.py /organized/behavioral
   ```

## Participant ID Detection

The script detects participant IDs in two ways:

1. **Folder Names**: Looks for directories that start with `sub-` (e.g., `sub-001`, `sub-123`)
2. **File Names**: Searches for `sub-` pattern in filenames (e.g., `sub-001_task-rest.mp4`)

## Target Directory Structure

Files are moved to: `target_directory/participant_id/beh/filename`

Example:
```
organized_data/
├── sub-001/
│   └── beh/
│       ├── sub-001_task-rest.mp4
│       └── sub-001_task-nback.avi
├── sub-002/
│   └── beh/
│       └── sub-002_behavioral_video.mov
└── sub-003/
    └── beh/
        └── sub-003_experiment.mkv
```

## Supported Video Formats

- `.mp4` - MPEG-4 Video
- `.avi` - Audio Video Interleave
- `.mov` - QuickTime Movie
- `.mkv` - Matroska Video
- `.wmv` - Windows Media Video
- `.flv` - Flash Video
- `.webm` - WebM Video
- `.m4v` - iTunes Video
- `.3gp` - 3GPP Mobile Video
- `.mpg` / `.mpeg` - MPEG Video
- `.m2v` - MPEG-2 Video
- `.asf` - Advanced Systems Format

## Logging

The tool creates a comprehensive log file that includes:
- Start and end timestamps
- List of all video files found
- Successful move operations
- Failed operations with error details
- Files without detectable participant IDs
- Summary statistics

## Error Handling

- **Missing Source Directory**: Script exits with error message
- **Permission Issues**: Logged as failed moves, processing continues
- **Filename Conflicts**: Automatic timestamp addition to create unique names
- **Missing Participant ID**: Files are logged but not moved
- **Invalid Video Files**: Non-video files are ignored

## Testing

### Test the Video File Mover
Run the test script to verify functionality:
```bash
python3 test_video_mover.py
```

### Test the Empty Beh Cleanup
Run the test script to verify cleanup functionality:
```bash
python3 test_empty_beh_cleanup.py
```

## Usage Examples and Demonstrations

### Video File Mover Demo
Run the usage example script for a demonstration:
```bash
python3 usage_example.py
```

For detailed help:
```bash
python3 usage_example.py --help
```

### Empty Beh Cleanup Demo
Run the cleanup usage example for a demonstration:
```bash
python3 usage_example_cleanup.py
```

For detailed help:
```bash
python3 usage_example_cleanup.py --help
```

## Requirements

- Python 3.6 or higher
- Standard library modules (no additional dependencies required)

## Important Notes

- **Files are moved, not copied**: Original files are deleted from the source location
- **Backup recommended**: Consider backing up your data before running the script
- **Permission requirements**: Ensure you have read/write permissions for both source and target directories
- **Dry run**: Consider testing with a small subset of files first

## Troubleshooting

1. **No files moved**: Check if video files contain participant ID patterns
2. **Permission errors**: Verify read/write access to directories
3. **Missing files**: Check the log file for detailed error information
4. **Unexpected behavior**: Run the test script to verify functionality

## Safety Features

- Creates target directories automatically
- Handles filename conflicts gracefully
- Comprehensive error logging
- Continues processing even if individual files fail
- Validates input directories before processing
