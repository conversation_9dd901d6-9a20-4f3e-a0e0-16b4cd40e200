#!/usr/bin/env python3
"""
Usage Example for Video File Mover

This script demonstrates how to use the video_file_mover.py script
with various scenarios and provides example usage patterns.
"""

import os
import tempfile
from pathlib import Path
import subprocess
import sys


def create_sample_structure():
    """
    Create a sample directory structure for demonstration
    
    Returns:
        tuple: (source_directory, target_directory)
    """
    # Create temporary directories
    temp_dir = tempfile.mkdtemp(prefix="video_mover_demo_")
    source_dir = Path(temp_dir) / "source_data"
    target_dir = Path(temp_dir) / "organized_data"
    
    # Create source directory structure
    sample_structure = [
        "raw_recordings/sub-001/behavioral_data/sub-001_task-rest_run-01.mp4",
        "raw_recordings/sub-001/behavioral_data/sub-001_task-nback_run-02.avi",
        "raw_recordings/sub-002/session1/sub-002_task-rest.mov",
        "raw_recordings/sub-002/session1/sub-002_task-stroop.mkv",
        "batch_01/recordings/sub-003_behavioral_video.mp4",
        "batch_01/recordings/sub-004_experiment.wmv",
        "misc_files/sub-005/video_recording.mp4",
        "backup/old_data/sub-006_backup_video.avi",
        "unorganized/random_video_sub-007.mp4",
        "participant_data/sub-008/ses-001/sub-008_task-memory.mov",
        "no_participant_folder/some_video.mp4",  # This won't be moved
        "raw_recordings/sub-001/notes.txt",  # Not a video file
    ]
    
    print(f"📁 Creating sample structure in: {source_dir}")
    
    for file_path in sample_structure:
        full_path = source_dir / file_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        # Create sample files with some content
        with open(full_path, 'w') as f:
            f.write(f"Sample video file: {full_path.name}\n")
            f.write(f"Original location: {full_path}\n")
    
    target_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ Sample structure created!")
    print(f"   Source: {source_dir}")
    print(f"   Target: {target_dir}")
    
    return str(source_dir), str(target_dir)


def demonstrate_usage():
    """
    Demonstrate various usage scenarios of the video file mover
    """
    print("🎬 Video File Mover - Usage Examples")
    print("=" * 60)
    
    # Create sample data
    source_dir, target_dir = create_sample_structure()
    
    print(f"\n📋 Example 1: Basic usage")
    print(f"Command: python3 video_file_mover.py {source_dir} {target_dir}")
    print(f"This will move all video files from {source_dir} to organized structure in {target_dir}")
    
    print(f"\n📋 Example 2: With custom log file")
    print(f"Command: python3 video_file_mover.py {source_dir} {target_dir} custom_log.txt")
    print(f"This will use 'custom_log.txt' instead of the default log file")
    
    print(f"\n📂 Current source directory structure:")
    for root, dirs, files in os.walk(source_dir):
        level = root.replace(source_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")
    
    # Actually run the mover
    script_path = Path(__file__).parent / "video_file_mover.py"
    log_file = Path(target_dir).parent / "demo_log.txt"
    
    print(f"\n🚀 Running the video file mover...")
    try:
        result = subprocess.run([
            sys.executable, str(script_path), 
            source_dir, target_dir, str(log_file)
        ], capture_output=True, text=True, check=True)
        
        print("✅ Video file mover completed successfully!")
        print(f"Output:\n{result.stdout}")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running video file mover: {e}")
        print(f"Error output: {e.stderr}")
        return
    
    print(f"\n📂 Resulting target directory structure:")
    for root, dirs, files in os.walk(target_dir):
        level = root.replace(target_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")
    
    # Show log file contents
    if os.path.exists(log_file):
        print(f"\n📝 Log file contents ({log_file}):")
        with open(log_file, 'r') as f:
            for line in f:
                print(f"   {line.strip()}")
    
    # Clean up
    print(f"\n🧹 Demo files created in: {Path(source_dir).parent}")
    print(f"You can delete this directory when done: rm -rf {Path(source_dir).parent}")


def show_help():
    """Show detailed help and usage information"""
    print("🎬 Video File Mover - Detailed Usage Guide")
    print("=" * 60)
    
    print("""
📖 DESCRIPTION:
   This tool moves video files from a source directory structure to an organized
   target directory with the format: target_directory/participant_id/beh/
   
🎯 SUPPORTED VIDEO FORMATS:
   .mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .m4v, .3gp, .mpg, .mpeg, .m2v, .asf
   
🔍 PARTICIPANT ID DETECTION:
   The script looks for participant IDs in two ways:
   1. Folder names that start with 'sub-' (e.g., sub-001, sub-123)
   2. Filenames that contain 'sub-' pattern (e.g., sub-001_task-rest.mp4)
   
📁 TARGET STRUCTURE:
   Files are moved to: target_directory/participant_id/beh/filename
   Example: /organized_data/sub-001/beh/sub-001_task-rest.mp4
   
⚠️  IMPORTANT NOTES:
   - Files are MOVED, not copied (original files are deleted)
   - If target file exists, a timestamp is added to avoid conflicts
   - Files without detectable participant IDs are logged but not moved
   - All operations are logged for troubleshooting
   
💻 COMMAND LINE USAGE:
   python3 video_file_mover.py <source_directory> <target_directory> [log_file]
   
   Arguments:
   - source_directory: Directory to search for video files (required)
   - target_directory: Where to move organized files (required)  
   - log_file: Custom log filename (optional, default: video_move_log.txt)
   
📚 EXAMPLES:
   
   Basic usage:
   python3 video_file_mover.py /path/to/raw/data /path/to/organized/data
   
   With custom log:
   python3 video_file_mover.py /raw/data /organized/data my_log.txt
   
   Real-world example:
   python3 video_file_mover.py /Volumes/RawData/Recordings /Volumes/OrganizedData/Behavioral
   
🔧 TROUBLESHOOTING:
   - Check the log file for detailed information about any failed moves
   - Ensure you have write permissions to the target directory
   - Verify that video files follow expected naming conventions
   - Make sure the source directory exists and contains video files
    """)


def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
    else:
        demonstrate_usage()


if __name__ == "__main__":
    main()
