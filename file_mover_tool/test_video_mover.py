#!/usr/bin/env python3
"""
Test script for video_file_mover.py

This script creates a test environment with sample video files and tests
the video file mover functionality.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the current directory to the path to import the video_file_mover module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from video_file_mover import (
    extract_participant_id,
    extract_participant_id_from_filename,
    is_video_file,
    find_video_files,
    create_target_structure,
    process_video_files
)


def create_test_structure(base_dir):
    """
    Create a test directory structure with sample video files
    
    Args:
        base_dir (str): Base directory for the test structure
    """
    # Create test directories
    test_dirs = [
        "sub-001/ses-001/beh",
        "sub-002/ses-001/beh", 
        "sub-003/ses-002/beh",
        "random_folder/sub-004",
        "another_folder/nested/sub-005",
        "no_sub_folder"
    ]
    
    for dir_path in test_dirs:
        full_path = Path(base_dir) / dir_path
        full_path.mkdir(parents=True, exist_ok=True)
    
    # Create sample video files
    test_files = [
        "sub-001/ses-001/beh/sub-001_task-rest_run-01.mp4",
        "sub-001/ses-001/beh/sub-001_task-task_run-02.avi",
        "sub-002/ses-001/beh/sub-002_task-rest.mov",
        "sub-003/ses-002/beh/sub-003_recording.mkv",
        "random_folder/sub-004/video_sub-004_test.mp4",
        "another_folder/nested/sub-005/sub-005_behavior.wmv",
        "no_sub_folder/some_video.mp4",  # This should not be moved
        "sub-006/test.txt",  # Not a video file
        "sub-007/video.mp4"  # Should be moved
    ]
    
    for file_path in test_files:
        full_path = Path(base_dir) / file_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        # Create empty files
        full_path.touch()
    
    print(f"✅ Created test structure in: {base_dir}")
    return test_files


def test_participant_id_extraction():
    """Test participant ID extraction functions"""
    print("\n🧪 Testing participant ID extraction...")
    
    # Test cases
    test_cases = [
        ("/path/to/sub-001/ses-001/file.mp4", "sub-001"),
        ("/path/sub-123/another/path.avi", "sub-123"),
        ("/no/sub/here/file.mov", None),
        ("sub-001_task-rest.mp4", "sub-001"),
        ("video_sub-456_test.avi", "sub-456"),
        ("nosub_here.mp4", None)
    ]
    
    for test_path, expected in test_cases:
        # Test path-based extraction
        result = extract_participant_id(test_path)
        status = "✅" if result == expected else "❌"
        print(f"{status} Path '{test_path}' -> '{result}' (expected: '{expected}')")
        
        # Test filename-based extraction
        filename = Path(test_path).name
        result = extract_participant_id_from_filename(filename)
        # Only test filename extraction for actual filenames
        if "task-" in filename or "sub-" in filename:
            status = "✅" if result == expected else "❌"
            print(f"{status} Filename '{filename}' -> '{result}' (expected: '{expected}')")


def test_video_file_detection():
    """Test video file detection"""
    print("\n🧪 Testing video file detection...")
    
    test_files = [
        ("video.mp4", True),
        ("movie.avi", True),
        ("clip.mov", True),
        ("document.txt", False),
        ("image.jpg", False),
        ("audio.mp3", False),
        ("Video.MP4", True),  # Test case insensitive
        ("file.mkv", True)
    ]
    
    for filename, expected in test_files:
        result = is_video_file(filename)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{filename}' -> {result} (expected: {expected})")


def test_file_mover():
    """Test the complete file moving functionality"""
    print("\n🧪 Testing complete file mover functionality...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create source and target directories
        source_dir = Path(temp_dir) / "source"
        target_dir = Path(temp_dir) / "target"
        
        source_dir.mkdir()
        target_dir.mkdir()
        
        # Create test structure
        create_test_structure(source_dir)
        
        # Count initial video files
        initial_videos = find_video_files(str(source_dir))
        print(f"📊 Found {len(initial_videos)} video files in source")
        
        # Process the files
        stats = process_video_files(str(source_dir), str(target_dir))
        
        # Print results
        print(f"📊 Move statistics:")
        print(f"   Total found: {stats['total_found']}")
        print(f"   Successfully moved: {stats['successfully_moved']}")
        print(f"   Failed moves: {stats['failed_moves']}")
        print(f"   No participant ID: {stats['no_participant_id']}")
        
        # Check target structure
        print(f"\n📁 Target directory structure:")
        for root, dirs, files in os.walk(target_dir):
            level = root.replace(str(target_dir), '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                print(f"{subindent}{file}")
        
        # Verify some files were moved
        if stats['successfully_moved'] > 0:
            print("✅ File mover test completed successfully!")
        else:
            print("❌ No files were moved - check the implementation!")


def main():
    """Run all tests"""
    print("🧪 Running Video File Mover Tests")
    print("=" * 50)
    
    test_participant_id_extraction()
    test_video_file_detection()
    test_file_mover()
    
    print("\n🏁 All tests completed!")


if __name__ == "__main__":
    main()
