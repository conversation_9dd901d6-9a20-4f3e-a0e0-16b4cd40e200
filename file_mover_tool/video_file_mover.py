#!/usr/bin/env python3
"""
Video File Mover Script

This script goes through all subdirectories of a given source directory, finds all video files,
and moves them to the correct participantid/beh subfolder under the given target directory.
It logs any failed movements and doesn't copy files - only moves them.

Usage:
    python3 video_file_mover.py <source_directory> <target_directory> [log_file]

Arguments:
    source_directory: Path to the source directory to search for video files
    target_directory: Path to the target directory where files will be moved
    log_file: Optional log filename (default: video_move_log.txt)

Video file extensions supported:
    .mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .m4v, .3gp, .mpg, .mpeg, .m2v, .asf
"""

import os
import sys
import shutil
import re
import logging
from pathlib import Path
from datetime import datetime
import argparse


# Video file extensions to look for
VIDEO_EXTENSIONS = {
    '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', 
    '.m4v', '.3gp', '.mpg', '.mpeg', '.m2v', '.asf'
}


def setup_logging(log_file):
    """
    Set up logging configuration
    
    Args:
        log_file (str): Path to the log file
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )


def extract_participant_id(filepath):
    """
    Extract participant ID from filepath by finding folder names that start with 'sub-'
    
    Args:
        filepath (str): Full path to the file
        
    Returns:
        str: Participant ID (the sub- folder name) or None if not found
    """
    path_parts = Path(filepath).parts
    for part in path_parts:
        if part.startswith('sub-'):
            return part
    return None


def extract_participant_id_from_filename(filename):
    """
    Extract participant ID from filename using pattern sub-<id>
    
    Args:
        filename (str): Name of the file
        
    Returns:
        str: Participant ID or None if not found
    """
    # Look for sub- pattern in filename with alphanumeric ID
    match = re.search(r'sub-([A-Za-z0-9]+)', filename)
    if match:
        return f"sub-{match.group(1)}"
    return None


def is_video_file(filepath):
    """
    Check if the file is a video file based on its extension
    
    Args:
        filepath (str): Path to the file
        
    Returns:
        bool: True if it's a video file, False otherwise
    """
    return Path(filepath).suffix.lower() in VIDEO_EXTENSIONS


def find_video_files(source_directory):
    """
    Recursively find all video files in the source directory
    
    Args:
        source_directory (str): Path to the source directory
        
    Returns:
        list: List of video file paths
    """
    video_files = []
    source_path = Path(source_directory)
    
    if not source_path.exists():
        logging.error(f"Source directory does not exist: {source_directory}")
        return video_files
    
    logging.info(f"🔍 Searching for video files in: {source_directory}")
    
    for root, dirs, files in os.walk(source_directory):
        for file in files:
            filepath = os.path.join(root, file)
            if is_video_file(filepath):
                video_files.append(filepath)
                logging.info(f"Found video file: {filepath}")
    
    logging.info(f"📊 Total video files found: {len(video_files)}")
    return video_files


def create_target_structure(target_directory, participant_id):
    """
    Create the target directory structure: target_directory/participant_id/beh
    
    Args:
        target_directory (str): Base target directory
        participant_id (str): Participant ID (e.g., sub-001)
        
    Returns:
        str: Path to the beh directory
    """
    beh_path = Path(target_directory) / participant_id / "beh"
    beh_path.mkdir(parents=True, exist_ok=True)
    return str(beh_path)


def move_video_file(source_file, target_directory, participant_id):
    """
    Move a video file to the target directory structure
    
    Args:
        source_file (str): Path to the source video file
        target_directory (str): Base target directory
        participant_id (str): Participant ID
        
    Returns:
        bool: True if successful, False if failed
    """
    try:
        # Create target structure
        beh_path = create_target_structure(target_directory, participant_id)
        
        # Get filename
        filename = Path(source_file).name
        target_file = os.path.join(beh_path, filename)
        
        # Check if target file already exists
        if os.path.exists(target_file):
            logging.warning(f"Target file already exists: {target_file}")
            # Create a unique filename by adding timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name_parts = Path(filename).stem, timestamp, Path(filename).suffix
            new_filename = f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
            target_file = os.path.join(beh_path, new_filename)
            logging.info(f"Renamed to avoid conflict: {new_filename}")
        
        # Move the file
        shutil.move(source_file, target_file)
        logging.info(f"✅ Successfully moved: {source_file} -> {target_file}")
        return True
        
    except Exception as e:
        logging.error(f"❌ Failed to move {source_file}: {str(e)}")
        return False


def process_video_files(source_directory, target_directory):
    """
    Process all video files and move them to the correct structure
    
    Args:
        source_directory (str): Source directory to search
        target_directory (str): Target directory for organized files
        
    Returns:
        dict: Statistics about the move operation
    """
    stats = {
        'total_found': 0,
        'successfully_moved': 0,
        'failed_moves': 0,
        'no_participant_id': 0
    }
    
    # Find all video files
    video_files = find_video_files(source_directory)
    stats['total_found'] = len(video_files)
    
    if not video_files:
        logging.info("No video files found in the source directory.")
        return stats
    
    # Process each video file
    logging.info(f"🚀 Starting to move {len(video_files)} video files...")
    
    for video_file in video_files:
        # Try to extract participant ID from file path first
        participant_id = extract_participant_id(video_file)
        
        # If not found in path, try filename
        if not participant_id:
            participant_id = extract_participant_id_from_filename(Path(video_file).name)
        
        if not participant_id:
            logging.warning(f"⚠️  Could not determine participant ID for: {video_file}")
            stats['no_participant_id'] += 1
            continue
        
        logging.info(f"📁 Moving file for participant: {participant_id}")
        
        # Move the file
        if move_video_file(video_file, target_directory, participant_id):
            stats['successfully_moved'] += 1
        else:
            stats['failed_moves'] += 1
    
    return stats


def print_summary(stats):
    """
    Print a summary of the move operation
    
    Args:
        stats (dict): Statistics from the move operation
    """
    print("\n" + "="*60)
    print("📊 VIDEO FILE MOVE SUMMARY")
    print("="*60)
    print(f"Total video files found: {stats['total_found']}")
    print(f"Successfully moved: {stats['successfully_moved']}")
    print(f"Failed moves: {stats['failed_moves']}")
    print(f"Files without participant ID: {stats['no_participant_id']}")
    print("="*60)
    
    if stats['failed_moves'] > 0 or stats['no_participant_id'] > 0:
        print("⚠️  Check the log file for details about failed operations.")
    else:
        print("✅ All video files were processed successfully!")


def main():
    """
    Main function to handle command line arguments and orchestrate the file moving process
    """
    parser = argparse.ArgumentParser(
        description="Move video files to organized participant/beh structure",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python3 video_file_mover.py /path/to/source /path/to/target
    python3 video_file_mover.py /path/to/source /path/to/target custom_log.txt
        """
    )
    
    parser.add_argument(
        'source_directory',
        help='Source directory to search for video files'
    )
    
    parser.add_argument(
        'target_directory', 
        help='Target directory where files will be moved to participant/beh structure'
    )
    
    parser.add_argument(
        'log_file',
        nargs='?',
        default='video_move_log.txt',
        help='Log file for the operation (default: video_move_log.txt)'
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not os.path.exists(args.source_directory):
        print(f"❌ Error: Source directory does not exist: {args.source_directory}")
        sys.exit(1)
    
    # Create target directory if it doesn't exist
    Path(args.target_directory).mkdir(parents=True, exist_ok=True)
    
    # Set up logging
    setup_logging(args.log_file)
    
    logging.info("🎬 Starting Video File Mover")
    logging.info(f"Source directory: {args.source_directory}")
    logging.info(f"Target directory: {args.target_directory}")
    logging.info(f"Log file: {args.log_file}")
    
    # Process the files
    stats = process_video_files(args.source_directory, args.target_directory)
    
    # Print summary
    print_summary(stats)
    
    logging.info("🏁 Video File Mover completed")


if __name__ == "__main__":
    main()
